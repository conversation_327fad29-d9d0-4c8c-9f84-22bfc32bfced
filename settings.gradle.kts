//System.setProperty("MW_APP_PROJECT_NAME", "run-gpark-dev")


rootProject.name = "MetaApp International"
include(
    ":dev",
    ":asset_engine_obb",
    ":run-gpark-dev",
    ":run-party",
    ":libs3rdSdk",
    ":libs3rdSdkDanmaku",
)

includeBuild("depVersionConstraints")
//includeBuild("../metalib") { // 注意路径相对于当前项目的根目录
//    dependencySubstitution {
//        // biz-friend
//        substitute(module("com.bin.android:biz-friend"))
//            .using(project(":biz:friend"))
//        // biz-im
//        substitute(module("com.bin.android:biz-im"))
//            .using(project(":biz:im"))
//        // lib-metacloud
//        substitute(module("com.bin.android:lib-metacloud"))
//            .using(project(":lib:metacloud"))
//        // lib-metacloud-tencent
//        substitute(module("com.bin.android:lib-metacloud-tencent"))
//            .using(project(":lib:metacloud-tencent"))
//    }
//}
