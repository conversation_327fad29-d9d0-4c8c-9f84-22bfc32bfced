## 基本架构
dev: 源代码模块，基本上所有的开发代码都在这里，这里分开了不同的flavor用于区分不同的环境包
* product dimension: 区分海外和国内环境，区分Gpark和233派对
* channel dimension: 区分渠道，目前只有douyin和def默认无渠道
* 由于Gpark没有douyin渠道，是在dev的build.gradle.kts里面使用beforeVariants回调做逻辑处理屏蔽了这个GparkDouyin的组合
run-gpark-dev: Gpark调试模块，不会有具体的海外SDK实现，具体的实现在另外一个项目
* 这里是Gpark的调试壳子，有空实现，使用missingDimensionStrategy的方式构建了默认的Flavor去控制dev模块的Flavor保持是gpark和def
run-party:233派对的调试以及打包模块，派对相关配置都在这里
* 派对的壳子，使用missingDimensionStrategy的方式构建了默认的Flavor去控制dev模块的Flavor
* 因为有douyin渠道，所以配置了两个flavor去控制dev模块的Flavor，用于区分不同业务
depVersionConstraints: 打包相关的脚本代码以及构建参数在这里，以理解为默认实现。
* 如果有差异的包，可以在dev模块不同的flavor中配置不同的参数，会覆盖掉默认配置
* run-gpark-dev 或者 run-party 中如果也配置了对应的参数，还会覆盖掉flavor的配置，优先级最高
