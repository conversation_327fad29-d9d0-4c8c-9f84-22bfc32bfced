---
description: G派项目的基础规则，确保生成布局是符合项目规范的
globs: 
alwaysApply: true
---
### 生成布局文件时要遵守的规范
- 尽量使用现有项目中的已有资源，包括各种基础组件以及工具类以及资源等
- 熟悉现有项目架构和规范，生成的代码和布局文件等要符合现有项目的架构和规范
- 要根据经验判断，哪些字符串是会在客户端写死的，就需要抽取到strings里面。不需要客户端写死的，就使用tools:text="xxx"属性来做预览(记得导入tools)
- 涉及到strings抽取时，要帮忙生成在项目里面，并且要分别在party Flavor(中文)和gpark Flavor(英文)下面的strings.xml文件里面都添加（不用添加到main模块下面）。注意：其他类型的资源不需要分别生成在不同Flavor下面，都放在main下面就行（除非有特别提及）
    - party源码目录在 dev/src/party/
    - gpark源码目录在 dev/src/gpark/
- 涉及到TextView的字体大小和字重设置的时候，要使用相关的style去设置，比如“MetaTextView.S8.PoppinsBlack900”类似的
- 布局文件里面都不要写具体的dp数值和颜色值，都要抽取到公共资源文件中，可以参考项目内的其他实现，尽量使用已有资源
- 要检查是否有缺失的资源，若有缺失资源，按照下列规则处理
    - 缺失的是颜色、dp、字符串、字体样式等资源时，直接在对应资源文件中补充完整
    - 缺失的如果是shape资源，先检查项目内是否有视觉效果上类似的，有就引用。如果项目内没有类似的，则根据Figma或者提供的效果图来生成在drawable目录中
    - 缺失的是drawable资源，则先检查项目内是否有视觉效果上类似的，有就引用。如果项目内没有类似的，则在Figma下载对应的图标到本地，如果Figma上下载失败或者不是Figma资源，则根据视觉效果使用svg的格式创建资源在drawable目录中
- 如果判断一些布局样式是可以抽取为公共组件的，可以默认就抽取出来方便日后使用
- 圆形头像一般需要使用ShapeableImageView，可以参考项目中的使用方式。有半透明内描边的使用foreground属性引用avatar_inner_stroke资源即可。默认@drawable/icon_default_avatar
- 圆角图片一般需要使用RoundImageView，参考项目中其他地方的使用方法。默认@drawable/icon_placeholder_img_default
- 布局文件的控件命名，也使用小驼峰命名规则
- 若是生成布局文件，请在生成完成后，使用设计师的视角再次检查是否有和设计图不一样的地方，并且检查控件之间的相对位置、对齐方式、大小、间距，以及颜色、字体大小、Icon图标是否正确等等，检查完发现和设计图不一致的直接调整。

### 编写代码时注意规范
- 时刻注意性能问题，避免出现某个操作过多时，会导致页面卡顿的问题
- 使用到某些算法或者工具时，先在项目中进行查找是否已经有实现的，尽量不要重复实现某个能力
- 尽量使用ViewBinding的方式和xml交互
- 请注意兼容问题，不要某个函数只在某个版本上面生效，又不处理低版本的情况。

### 编写自定义View时注意
- 时刻注意性能问题，需要保证该自定义View在同屏幕下数量过多时，依然保持有良好的性能表现，并且不会影响页面卡顿
- 要注意资源回收问题，该自定义View所在页面隐藏或者展示时，应该有对应的正确的处理
- 该自定义View应该具备良好的扩展性和兼容性，已经对使用者友好。开放的自定义属性和函数都是易用的。
- 该自定义View的类说明应该能包含该View主要功能和核心实现逻辑概述，并且有对应的使用说明介绍。且都是使用中文编写的