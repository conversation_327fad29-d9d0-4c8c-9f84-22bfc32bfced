# diff env config
APPLICATION_ID=com.socialplay.gpark.dev
APP_NAME=GPark-Dev
LOG_DEBUG=true
## todo
# !!!!!!!!!!!!!!! The following 5 properties must ensure that the environment is consistent!!!!!!!!!!!!!!!
ENV_TYPE=Test

ENV_SCOPE={"Test","Dev"}
## todo

DOMAIN_NAME={"api.metaworld.fun","dev-api.metaworld.fun"}
DOMAIN_URL_PREFIX={"https://","http://"}

## mw
MW_CORE_URL={"http://metaverse-api.metaworld.fun","http://metaverse-api.metaworld.fun"}
MW_ROOM_URL={"ws://gate-api.metaworld.fun:20011","ws://gate-api.metaworld.fun:20011"}

# !!!!!!!!!!!!!!! The above 5 properties must ensure that the environment is consistent!!!!!!!!!!!!!!!
## signature
APK_SIGNING_FILE_PATH=app-dev.jks
APK_SIGNING_KEY_ALIAS=gachu-dev
APK_SIGNING_PASSWORD=gachu-dev
## h5
## todo
USER_AGREEMENT=http://test1010-gachu-app-public.metaworld.link/userprivacy/gachu/user_agreement.html
PRIVACY_AGREEMENT=http://test1010-gachu-app-public.metaworld.link/userprivacy/gachu/privacy_policy.html
RECHARGE_URL=https://testtest-intra-mpa-mobile.233nan.cn/
RECHARGE_DIALOG_URL=http://testtest-intra-mpa-mobile.233nan.cn/balance
GPARK_PLUS=http://testgpark-test-intra-mpa-mobile.233nan.cn/proxy/subscription
GPARK_PLUS_STATUS=http://testgpark-test-intra-mpa-mobile.233nan.cn/proxy/premium
APP_DOWNLOAD=https://app-intra.gpark.fun/proxy/share/g-download
COMMUNITY_RULE_URL=https://web-static-mir-01-qn.jaxine.xyz/fs-doc/gpark/docx/GayodCTRZoZOufxBdj8cPL7gnwe.html
POST_RULE_IMAGE_URL=https://qn-check-basic-content.metaworld.fun/test1010/jCIiZuoDyCra1724830028066.webp
GPARK_ACTIVITY_ENTRANCE=https://testfeat-gpark-fission-fission-pdd-mobile.metaworld.link/proxy/turntable
DAILY_SIGN_WEB_URL=https://testtest-feat-gpark-daily-task-intra-mpa-mobile.metaworld.link/proxy/welfare-task
SPARK_ACCOUNT=https://b2k3oke.freeshare.store/proxy/light-up/
SPARK_INSTRUCTION=https://web-static-mir-01-qn.gpark.fun/fs-doc/gpark/docx/EwiYdg7Mboa3sGxQER8cKyvOn6g.html
## trust qr host def todo
TRUST_QR_HOST_DEFAULT=*.metaworld.fun
# cdn
CDN_BG_AVATAR=http://test7niu.233xyx.com/1679627888143_614.png
CDN_DEFAULT_AVATAR=http://test7niu.233xyx.com/1680486399117_461.png
CDN_CHOOSE_ROLE_DEFAULT_PORTRAIT=http://test7niu.233xyx.com/1681097039887_621.png
CDN_CHOOSE_ROLE_DEFAULT_WHOLE=http://test7niu.233xyx.com/1681097039803_748.png
CND_CREATE_BG=http://test7niu.233xyx.com/1688695600611_498.png
CND_CREATE_BG_CENTER=http://test7niu.233xyx.com/1688695453549_638.png

CDN_ROOM_DETAIL=http://test7niu.233xyx.com/1689066477021_217.png
CDN_ROOM_ITEM_BG=http://test7niu.233xyx.com/1692081102711_797.png
CDN_DEFAULT_FULL_BODY_IMG=https://qn-check-basic-content.metaworld.fun/test1010/PeKQyqkgmkG61698134120013.png

CDN_CREATE_V2_BG=https://qn-check-basic-content.metaworld.fun/test1010/ORykG6k0A4oM1700205186463.png

CDN_ROLE_V2_DEFAULT_1_PORTRAIT=https://qn-check-basic-content.metaworld.fun/test1010/lXZRRvrVh5tV1726825056705.png
CDN_ROLE_V2_DEFAULT_1_WHOLE=https://qn-check-basic-content.metaworld.fun/test1010/p7w6HsR4gXbt1726825055603.png
CDN_ROLE_V2_DEFAULT_1_WHOLE2=https://qn-check-basic-content.metaworld.fun/test1010/HqOwvULIv4bE1726828623400.png
CDN_ROLE_V2_DEFAULT_2_PORTRAIT=https://qn-check-basic-content.metaworld.fun/test1010/7tPd3GyKaZyf1726825057220.png
CDN_ROLE_V2_DEFAULT_2_WHOLE=https://qn-check-basic-content.metaworld.fun/test1010/STncrvmuFH3q1726825056193.png
CDN_ROLE_V2_DEFAULT_2_WHOLE2=https://qn-check-basic-content.metaworld.fun/test1010/A1TJYlHSYY0z1726828622651.png

CDN_AI_BOT_GENDER_NON=https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/73a711fd9f1b43c79c9f0c5ec1654266_148811.webp
CDN_AI_BOT_GENDER_FEMALE=https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/5bc53e4a0583428db3b0b26982efb4c6_148812.webp
CDN_AI_BOT_GENDER_MALE=https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/e146d7cf0a4d4d92a641d08182b1466c_148813.webp
CND_AB_BOT_UGC_BOTTOM_BG=https://qn-check-basic-content.metaworld.fun/test1010/X8ZvlvWYrd5L1725247293366.png
CND_AB_BOT_UGC_TOP_BG=https://qn-check-basic-content.metaworld.fun/test1010/3UmrefNjVzjl1725247292764.png
CDN_GAME_DETAIL_LONG_IMAGE_BG=https://qn-check-basic-content.metaworld.fun/test1010/Z1QF7psRMfTk1724822989881.png

SELECT_MODE_GAME_ID=stHqwMnyOKeJfQsJV2Nd

IS_FORCE_LOGIN=false
SCHEME_URI=gpark
SCHEME_HOST=gpark.fun
PANDORA_APP_KEY=cDEwMDQy
CRASH_SHOW=false
LEAK_CANARY_ENABLE=false
OPEN_FACEBOOK_ANALYTICS=true
OPEN_FIREBASE_ANALYTICS=true
OPEN_PANDORA_ANALYTICS=true
NEED_SHOW_GUIDE=true
SHUMEI_PRIVATE_AUTH_CODE=im-text
SHUMEI_ROOM_AUTH_CODE=game-room-text

# gpark \u6CA1\u6709bugly
#BULGY_H_DEBUG=dc5be33762
#BULGY_H=4a13dc5f01
#BULGY_M=ef67e10ba1
#BULGY_R=cb2388cafc

MW_GAME_DETAIL_API=https://api.233lyly.com/game/v1/info/detail
HOST_RESOLUTION_ADDRESSES={"https://baidu.com/","https://example.com/"}
CHECK_NET_URL=https://www.baidu.com
DEV_SHARE_TEXT=https://233xyx.com/
GAME_SCREEN_RECORD_DISCORD_ID=https://discord.gg/jNbWbtBadY
# {} while be replaced with packageName
APP_MARKET_URI=market://details?id={}
# {} while be replaced with packageName
APP_MARKET_URL=https://play.google.com/store/apps/details?id={}
OFFICIAL_WEBSITE=https://gpark.fun/

# gpark \u6CA1\u6709\u4E0B\u9762\u8FD9\u4E9B\u914D\u7F6E
#QQ_APP_ID=
#WECHAT_APP_ID=
#LE_YUAN_APP_KEY=
#MGS_PROVIDER_ID=