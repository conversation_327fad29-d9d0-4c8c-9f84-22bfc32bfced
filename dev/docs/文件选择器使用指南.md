# 文件选择器使用指南

## 需求描述

我们希望文件选择器默认打开"最近"页面（系统默认行为），但是希望能够默认选中"本周"这个过滤条件，让用户更容易找到最近的音频文件。

## 解决方案

### 1. 推荐方案：最近页面 + 本周过滤

```kotlin
// 打开"最近"页面并尝试默认选中本周过滤
audioFileLauncher.launchThisWeekAudioPicker(allowMultiple = true)
```

### 2. 通用"最近"页面方案

```kotlin
// 对任意文件类型使用"最近"页面 + 本周过滤
audioFileLauncher.launchRecentsWithWeekFilter(
    fileType = FilePickerUtil.FileType.AUDIO,
    allowMultiple = true
)
```

### 3. 灵活选择方案

```kotlin
// 可选择使用"最近"页面还是目录定位
audioFileLauncher.launchAudioPicker(
    allowMultiple = true,
    useRecentsWithWeekFilter = true, // true=最近页面, false=目录定位
    directory = FilePickerUtil.DefaultDirectory.AUDIO_ROOT
)
```

## 完整实现示例

```kotlin
class YourFragment : Fragment() {
    
    private lateinit var audioFileLauncher: ActivityResultLauncher<Intent>
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 注册音频文件选择器
        audioFileLauncher = registerAudioPicker(
            allowMultiple = true,
            initialDirectory = FilePickerUtil.DefaultDirectory.AUDIO_ROOT
        ) { audioFiles ->
            if (audioFiles.isNotEmpty()) {
                toast("选中 ${audioFiles.size} 个音频文件")
                handleAudioFiles(audioFiles)
            }
        }
        
        // 按钮点击事件
        btnSelectAudio.setOnClickListener {
            openAudioFileSelector()
        }
    }
    
    private fun openAudioFileSelector() {
        // 打开"最近"页面并尝试默认选中本周过滤（符合用户需求）
        audioFileLauncher.launchThisWeekAudioPicker(allowMultiple = true)
    }
    
    private fun handleAudioFiles(audioFiles: List<FilePickerUtil.FileInfo>) {
        audioFiles.forEach { audioFile ->
            // 处理选中的音频文件
            processAudioFile(audioFile)
        }
    }
}
```

## 其他使用场景

### 音乐文件选择（定位到音乐目录）
```kotlin
// 音乐选择器通常需要定位到音乐目录而不是"最近"页面
musicFileLauncher.launchMusicPicker(
    allowMultiple = false,
    useRecentsWithWeekFilter = false // 定位到音乐目录
)
```

### 通用文件选择器
```kotlin
// 对于图片、视频等其他文件类型也可以使用"最近"页面
imageFileLauncher.launchRecentsWithWeekFilter(
    fileType = FilePickerUtil.FileType.IMAGE,
    allowMultiple = true
)
```

## 目录选择说明

| 目录类型 | 适用场景 | URI |
|---------|----------|-----|
| `AUDIO_ROOT` | 所有音频文件（推荐） | `audio_root` |
| `MUSIC` | 音乐文件 | `audio_root` |
| `DCIM` | 录音文件 | `primary:DCIM` |
| `DOWNLOADS` | 下载文件 | `downloads` |

## 时间过滤选项

| 过滤选项 | 描述 | 时间范围 |
|----------|------|----------|
| `ALL` | 所有时间 | 无限制 |
| `THIS_WEEK` | 本周 | 最近7天 |
| `THIS_MONTH` | 本月 | 最近30天 |
| `RECENT` | 最近 | 最近3天 |

## 策略选择

| 策略 | 描述 | 推荐度 |
|------|------|--------|
| `SMART` | 智能选择（默认） | ⭐⭐⭐⭐⭐ |
| `GET_CONTENT` | 内容选择器 | ⭐⭐⭐⭐ |
| `OPEN_DOCUMENT` | 文档选择器 | ⭐⭐⭐ |

## 实现原理

### Intent 参数说明

我们通过以下 Intent 参数尝试控制文件选择器的行为：

```kotlin
// 尝试设置时间过滤参数
putExtra("android.provider.extra.RECENTS_TIME_FILTER", "week")
putExtra("android.provider.extra.TIME_FILTER_START", weekAgo)
putExtra("android.provider.extra.TIME_FILTER_END", System.currentTimeMillis())

// 尝试强制显示最近页面
putExtra("android.content.extra.SHOW_RECENTS", true)
putExtra("android.provider.extra.DEFAULT_TAB", "recents")
```

## 注意事项

1. **系统兼容性** - 不同厂商的文件管理器可能不支持所有参数
2. **回退机制** - 如果本周过滤参数不生效，会自动回退到标准文件选择器
3. **用户体验** - 即使过滤参数不生效，用户仍可以在"最近"页面手动选择"本周"
4. **测试建议** - 建议在主要设备上测试实际效果

## 兼容性测试

| 设备品牌 | 系统版本 | 本周过滤支持 | 备注 |
|----------|----------|-------------|------|
| 华为 | EMUI 12+ | 部分支持 | 可能需要手动选择 |
| 小米 | MIUI 13+ | 支持 | 效果较好 |
| 原生Android | 11+ | 支持 | 完全支持 |
| 三星 | One UI 4+ | 部分支持 | 取决于文件管理器版本 |

## 备用方案

如果"最近"页面的本周过滤不生效，可以手动切换到目录定位：

```kotlin
// 切换到目录定位模式
audioFileLauncher.launchAudioPicker(
    allowMultiple = true,
    useRecentsWithWeekFilter = false, // 使用目录定位
    directory = FilePickerUtil.DefaultDirectory.AUDIO_ROOT
)
``` 