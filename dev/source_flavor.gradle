apply from: 'source_flavor_config.gradle'

static def quote(Object value) {
    if (value == null) value = ""
    return '"' + value + '"'
}

android {
    flavorDimensions.addAll(project.ext.flavorDimensions)

    productFlavors {
        project.flavorConfig.each { name, config ->
            create("$name") {
                dimension config.dimension

                // dimension:name
                buildConfigField "String", "flavorName", quote("$config.dimension:${name}")

                project.flavorConfig.each {
                    buildConfigField "boolean", "is${it.key.capitalize()}Flavor", "${it.key == name}"
                }
                println("创建变体Flavor $name $config.dimension")
            }
        }
    }
}