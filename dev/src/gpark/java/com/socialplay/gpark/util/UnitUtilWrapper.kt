package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R

object UnitUtilWrapper {
    private const val MILLION = 1000_000L
    private const val BILLION = 1000_000_000L

    /**
     * 统一的代币数量显示转换方法
     */
    fun formatCoinCont(num: Long): String {
        return when {
            num >= BILLION -> {
                String.format("%.2fM", 999.99)
            }

            num >= MILLION -> {
                String.format("%.2fM", num.toDouble() / MILLION)
            }

            // 千分位加逗号
            else -> String.format("%,d", num)
        }
    }

    fun formatPlayTime(context: Context, sec: Long, default: String? = null): String {
        val result = StringBuilder()
        val residual = sec % UnitUtil.HOUR
        val hour = sec / UnitUtil.HOUR
        if (hour > 0) {
            result.append(context.getString(R.string.x_hour_shorthand, hour.toString())).append(" ")
        }
        val min = residual / 60
        result.append(context.getString(R.string.x_minute_shorthand, min.toString()))
        return result.toString()
    }
}