<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_long_image_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:visibility="invisible"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    tools:layout_height="match_parent"
    tools:layout_width="match_parent"
    tools:visibility="visible">

    <ImageView
        android:id="@+id/iv_long_image_screenshot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="match_parent"
        tools:layout_width="match_parent"
        tools:src="@color/black_50" />

    <View
        android:id="@+id/v_bg_bottom"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_94"
        android:background="@color/color_F6F6F6"
        app:layout_constraintBottom_toBottomOf="@id/iv_long_image_screenshot"
        app:layout_constraintEnd_toEndOf="@id/iv_long_image_screenshot"
        app:layout_constraintStart_toStartOf="@id/iv_long_image_screenshot" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_long_image_qr_code"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:background="@color/white"
        android:padding="@dimen/dp_2"
        app:cornerRadii="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="@id/v_bg_bottom"
        app:layout_constraintEnd_toStartOf="@id/tv_long_image_tips"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/v_bg_bottom"
        app:layout_constraintTop_toTopOf="@id/v_bg_bottom" />

    <ImageView
        android:id="@+id/iv_long_image_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_share_screenshot_logo"
        app:layout_constraintBottom_toTopOf="@id/tv_long_image_tips"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_tips"
        app:layout_constraintTop_toTopOf="@id/v_bg_bottom"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_tips"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="@dimen/dp_174"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_2"
        android:minHeight="@dimen/dp_15"
        android:text="@string/share_screenshot_scan_text"
        android:textSize="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="@id/v_bg_bottom"
        app:layout_constraintEnd_toEndOf="@id/v_bg_bottom"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_qr_code"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_logo"
        app:uiLineHeight="@dimen/dp_15" />

</androidx.constraintlayout.widget.ConstraintLayout>