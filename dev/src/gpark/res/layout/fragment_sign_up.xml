<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
        app:rightText="@string/login_skip" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/rl_content_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_16"
        app:layout_constraintBottom_toTopOf="@+id/cl_agreement"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:layout_constraintTop_toTopOf="parent">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/iv_login_account_tip"
                style="@style/MetaTextView.S28.PoppinsBlack900"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_black_900"
                android:text="@string/sign_up"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/vGoogle"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_60"
                android:layout_marginHorizontal="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/bg_stroke_corner_12"
                app:layout_constraintBottom_toTopOf="@id/tvOr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_login_account_tip"
                app:layout_constraintVertical_chainStyle="packed" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvGoogle"
                style="@style/MetaTextView.S14.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/intl_continue_with_google"
                android:textColor="@color/neutral_color_1"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@id/vGoogle"
                app:layout_constraintEnd_toEndOf="@id/vGoogle"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/ivGoogle"
                app:layout_constraintTop_toTopOf="@id/vGoogle" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivGoogle"
                android:layout_width="@dimen/dp_28"
                android:layout_height="@dimen/dp_28"
                android:layout_marginEnd="@dimen/dp_8"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_google"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tvGoogle"
                app:layout_constraintEnd_toStartOf="@id/tvGoogle"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="@id/vGoogle"
                app:layout_constraintTop_toTopOf="@id/tvGoogle"
                app:shapeAppearance="@style/circleStyle"
                tools:visibility="visible" />

            <View
                android:id="@+id/vLineOrL"
                android:layout_width="@dimen/dp_34"
                android:layout_height="@dimen/dp_1"
                android:background="@color/black_50"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tvOr"
                app:layout_constraintEnd_toStartOf="@id/tvOr"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvOr"
                tools:visibility="visible" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvOr"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:paddingHorizontal="@dimen/dp_8"
                android:text="@string/intl_or_all_cap"
                android:textColor="@color/black_50"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/input_account"
                app:layout_constraintEnd_toStartOf="@id/vLineOrR"
                app:layout_constraintStart_toEndOf="@id/vLineOrL"
                app:layout_constraintTop_toBottomOf="@id/vGoogle"
                app:layout_constraintVertical_chainStyle="packed"
                tools:visibility="visible" />

            <View
                android:id="@+id/vLineOrR"
                android:layout_width="@dimen/dp_34"
                android:layout_height="@dimen/dp_1"
                android:background="@color/black_50"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tvOr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvOr"
                app:layout_constraintTop_toTopOf="@id/tvOr"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginHorizontal="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/selector_bg_sign_up_focus"
                android:hint="@string/enter_account"
                android:textColorHint="@color/black_40"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/black_40"
                app:layout_constraintTop_toBottomOf="@id/tvOr">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_account"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp_48"
                    android:background="@color/transparent"
                    android:fontFamily="@font/poppins_regular_400"
                    android:inputType="textNoSuggestions"
                    android:maxLength="20"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:id="@+id/vAccountBlock"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clickable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_account"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintStart_toStartOf="@id/input_account"
                app:layout_constraintTop_toTopOf="@id/input_account" />

            <ImageView
                android:id="@+id/ivClearName"
                android:layout_width="@dimen/dp_40"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/dp_8"
                android:paddingHorizontal="@dimen/dp_8"
                android:src="@drawable/ic_clear_input"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_account"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintTop_toTopOf="@id/input_account"
                tools:visibility="visible" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_account_format_err_tip"
                style="@style/MetaTextView.S12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:gravity="center_vertical"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintStart_toStartOf="@id/input_account"
                app:layout_constraintTop_toBottomOf="@id/input_account"
                app:uiLineHeight="@dimen/sp_22"
                tools:text="@string/signup_account_input_error"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginHorizontal="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/selector_bg_sign_up_focus"
                android:hint="@string/enter_password"
                android:textColorHint="@color/black_40"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/black_40"
                app:layout_constraintTop_toBottomOf="@id/tv_account_format_err_tip"
                app:layout_goneMarginTop="@dimen/dp_12">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp_88"
                    android:background="@color/transparent"
                    android:fontFamily="@font/poppins_regular_400"
                    android:inputType="textNoSuggestions"
                    android:maxLength="16"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />

            </com.google.android.material.textfield.TextInputLayout>

            <ImageView
                android:id="@+id/ivClearPassword"
                android:layout_width="@dimen/dp_40"
                android:layout_height="0dp"
                android:paddingHorizontal="@dimen/dp_8"
                android:src="@drawable/ic_clear_input"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_password"
                app:layout_constraintEnd_toStartOf="@id/iv_password_visibility"
                app:layout_constraintTop_toTopOf="@id/input_password"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_password_visibility"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_16"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@id/input_password"
                app:layout_constraintEnd_toEndOf="@id/input_password"
                app:layout_constraintTop_toTopOf="@id/input_password" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_password_format_err_tip"
                style="@style/MetaTextView.S12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:gravity="center_vertical"
                android:textColor="@color/color_666666"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintStart_toStartOf="@id/input_password"
                app:layout_constraintTop_toBottomOf="@id/input_password"
                app:uiLineHeight="@dimen/sp_22"
                tools:text="@string/signup_password_input_error"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_login"
                style="@style/Button.S18.PoppinsBlack900.Height46"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_72"
                android:layout_marginTop="@dimen/dp_24"
                android:paddingTop="@dimen/dp_14"
                android:paddingBottom="@dimen/dp_14"
                android:text="@string/sign_up"
                android:textColor="@color/neutral_color_1"
                app:layout_constraintTop_toBottomOf="@id/tv_password_format_err_tip" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_agreement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_32"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_agreement"
            style="@style/MetaTextView.S12.PoppinsLight300"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:gravity="center"
            android:textColor="#53535E"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:uiLineHeight="@dimen/dp_18"
            tools:ignore="RtlSymmetry"
            tools:text="I have read and agreed to the user agreement and privacy policy" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>