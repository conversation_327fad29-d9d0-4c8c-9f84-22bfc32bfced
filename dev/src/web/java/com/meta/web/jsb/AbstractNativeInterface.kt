package com.meta.web.jsb

import android.content.Context
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.jsb.BaseNativeInterface
import com.meta.web.contract.IWebPlatformContract

abstract class AbstractNativeInterface(
    webCore: WebCore,
    protected val platformContract: IWebPlatformContract,
) : BaseNativeInterface(webCore) {

    protected val context: Context get() = webCore.context
    protected val webStorage: IWebPlatformContract.WebStorage get() = platformContract.webStorage

    @Deprecated("Compatibility usage only")
    private var legacyJsApi: IWebPlatformContract.LegacyJsApi? = null

    @Deprecated("Compatibility usage only")
    fun setLegacyJsApi(legacyJsApi: IWebPlatformContract.LegacyJsApi){
        this.legacyJsApi = legacyJsApi
    }
    @Deprecated("Compatibility usage only")
    fun getLegacyJsApi(): IWebPlatformContract.LegacyJsApi? {
        return legacyJsApi
    }

    override fun _executeLegacyJsApi(json: String): String? {
        return legacyJsApi?.exec(json)
    }
}