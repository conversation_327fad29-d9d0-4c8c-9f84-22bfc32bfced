package com.meta.web.jsb

import com.meta.lib.web.core.WebCore
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.helper.WebNavigationHelper
import com.meta.web.model.WebDialogArgs
import com.meta.web.ui.WebCoreDialog

internal class WebDialogNativeInterfaceImpl(
    webCore: WebCore,
    platformContract: IWebPlatformContract,
    private val dialog: WebCoreDialog,
    private val args: WebDialogArgs,
    private val navigationHelper: WebNavigationHelper,
) : FragmentNativeInterfaceImpl(webCore, platformContract, dialog) {


    override fun _closeAll(removeWebView: Boolean) {
        navigationHelper.navigateToPreviousPage()
    }

    override fun _goBack(): Boolean {
        navigationHelper.goBack(true, 1)
        return true
    }
}