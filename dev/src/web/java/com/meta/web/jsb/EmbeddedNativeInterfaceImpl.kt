package com.meta.web.jsb

import com.meta.lib.web.core.WebCore
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.model.EmbeddedWebFragmentArgs
import com.meta.web.ui.EmbeddedWebCoreFragment

internal class EmbeddedNativeInterfaceImpl(
    webCore: WebCore,
    platformContract: IWebPlatformContract,
    private val fragment: EmbeddedWebCoreFragment,
    private val args: EmbeddedWebFragmentArgs,
) : FragmentNativeInterfaceImpl(webCore, platformContract, fragment) {

    override fun _closeAll(removeWebView: Boolean) {
        // 直接嵌入的web页面，不支持关闭页面，需要扩展自己的WebContainer和NativeInterface实现
    }

    override fun _goBack(): Boolean {
        // 直接嵌入的web页面，只能在页面内部回退，如果确实有这种需求，需要扩展自己的WebContainer和NativeInterface实现
        return webCore.goBack()
    }
}