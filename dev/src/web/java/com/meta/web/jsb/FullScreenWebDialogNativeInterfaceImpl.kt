package com.meta.web.jsb

import com.meta.lib.web.core.WebCore
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.helper.WebNavigationHelper
import com.meta.web.model.FullScreenWebDialogArgs
import com.meta.web.model.WebDialogArgs
import com.meta.web.ui.FullScreenWebCoreDialog
import com.meta.web.ui.WebCoreDialog

internal class FullScreenWebDialogNativeInterfaceImpl(
    webCore: WebCore,
    platformContract: IWebPlatformContract,
    private val dialog: FullScreenWebCoreDialog,
    private val args: FullScreenWebDialogArgs,
    private val navigationHelper: WebNavigationHelper,
) : FragmentNativeInterfaceImpl(webCore, platformContract, dialog) {


    override fun _closeAll(removeWebView: Boolean) {
        navigationHelper.navigateToPreviousPage()
    }

    override fun _goBack(): Boolean {
        navigationHelper.goBack(true, 1)
        return true
    }
}