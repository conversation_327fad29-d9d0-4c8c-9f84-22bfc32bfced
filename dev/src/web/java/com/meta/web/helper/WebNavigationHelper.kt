package com.meta.web.helper

import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.container.IWebContainer
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList

internal class WebNavigationHelper(
    private val webCore: WebCore,
    private val fragment: Fragment,
    private val navigateUp: () -> Unit
) {

    private val navigationListeners = CopyOnWriteArrayList<IWebContainer.ContainerNavigationListener>()


    fun navigateToPreviousPage(){
        if(navigationListeners.lastOrNull { it.onWebContainerNavigateUp(webCore) } != null){
            return
        }
        navigateUp()
    }

    fun goBack(notifyWeb: Boolean, scene: Int) {
        if (webCore.canGoBack()) {
            if (navigationListeners.lastOrNull { it.onWebViewGoBack(webCore) } != null) {
                return
            }
            webCore.goBack()
            if (notifyWeb) {
                fragment.viewLifecycleOwner.lifecycleScope.launch {
                    delay(300L)
                    webCore.webInterface.backToWeb(scene)
                }
            }
        } else {
            navigateToPreviousPage()
        }
    }

    fun unregisterContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
        navigationListeners.remove(listener)
    }

    fun registerContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
        navigationListeners.add(listener)
    }

}