package com.meta.web.helper

import com.meta.lib.web.core.WebCore
import com.socialplay.gpark.R
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ToastUtil

class LoadingViewHelper(
    private val webCore: WebCore,
    private val loadingView: LoadingView,
    private val circleLoading: Boolean = true
) {

    init {
        loadingView.setRetry {
            if (!NetUtil.isNetworkAvailable()) {
                ToastUtil.showShort(R.string.net_unavailable)
            } else {
                webCore.reload()
            }
        }
    }

    fun closeLoading() {
        loadingView.hide()
    }

    fun showLoading() {
        loadingView.showLoading()
    }

    fun showError(message: String?) {
        loadingView.showError()
    }

}