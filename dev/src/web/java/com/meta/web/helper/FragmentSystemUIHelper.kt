package com.meta.web.helper

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.meta.web.model.SystemUiSettings

class FragmentSystemUIHelper(
    private val fragment:Fragment
) :LifecycleEventObserver{

    companion object {
        private const val KEY_SYSTEM_UUI_SETTING_BACKUP = "key.system.ui.setting.backup"
        private const val KEY_SYSTEM_UUI_SETTING = "key.system.ui.setting"
    }

    private var systemUiSettings: SystemUiSettings? = null
    private var systemUiSettingsBackup: SystemUiSettings? = null

    init {
        fragment.lifecycle.addObserver(this)
    }

    fun restoreInstance(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            if(savedInstanceState.containsKey(KEY_SYSTEM_UUI_SETTING_BACKUP)){
                systemUiSettingsBackup = savedInstanceState.getParcelable(KEY_SYSTEM_UUI_SETTING_BACKUP)
            }

            if(savedInstanceState.containsKey(KEY_SYSTEM_UUI_SETTING)){
                systemUiSettings = savedInstanceState.getParcelable(KEY_SYSTEM_UUI_SETTING)
            }
        }
    }

    fun saveInstance(outState: Bundle) {
        systemUiSettingsBackup?.let { outState.putParcelable(KEY_SYSTEM_UUI_SETTING_BACKUP, it) }
        systemUiSettings?.let { outState.putParcelable(KEY_SYSTEM_UUI_SETTING, it) }
    }

    fun backupSystemUiSettingsIfNeeded(systemUiVisibility: Int? = null, orientation: Int? = null){
        if (systemUiSettingsBackup == null) {
            systemUiSettingsBackup = SystemUiSettings()
        }

        if(systemUiVisibility != null && systemUiSettingsBackup?.systemUiVisibility == null){
            systemUiSettingsBackup = systemUiSettingsBackup?.copy(systemUiVisibility = systemUiVisibility)
        }

        if(orientation != null && systemUiSettingsBackup?.orientation == null){
            systemUiSettingsBackup = systemUiSettingsBackup?.copy(orientation = orientation)
        }
    }

    fun setSystemUiSettings(systemUiVisibility: Int? = null, orientation: Int? = null) {
        if(systemUiSettings == null){
            systemUiSettings = SystemUiSettings()
        }

        if(systemUiVisibility != null){
            systemUiSettings = systemUiSettings?.copy(systemUiVisibility = systemUiVisibility)
        }

        if(orientation != null){
            systemUiSettings = systemUiSettings?.copy(orientation = orientation)
        }
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        val activity = fragment.requireActivity()
        val decorView = activity.window.decorView

        when (event) {
            Lifecycle.Event.ON_RESUME -> {//在回到我们的页面时，恢复当前页面的系统UI状态
                systemUiSettings?.let {
                    it.orientation?.let { activity.requestedOrientation = it }
                    it.systemUiVisibility?.let { decorView.systemUiVisibility = it}
                }
            }
            Lifecycle.Event.ON_PAUSE -> { // 在离开我们的页面时，还之前的系统UI状态
                systemUiSettingsBackup?.let {
                    it.orientation?.let { activity.requestedOrientation = it }
                    it.systemUiVisibility?.let { decorView.systemUiVisibility = it }
                }
            }

            else -> {

            }
        }
    }

}