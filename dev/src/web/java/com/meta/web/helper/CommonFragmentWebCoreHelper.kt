package com.meta.web.helper

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.meta.lib.web.core.extension.toUri
import com.meta.web.constans.EventConstants
import com.meta.web.ui.IFragmentWebContainer
import com.socialplay.gpark.function.analytics.Analytics

internal class CommonFragmentWebCoreHelper(
    private val fragmentWebContainer: IFragmentWebContainer
) {

    fun onFragmentCreated() {
        fragmentWebContainer.fragment.lifecycle.addObserver(object : LifecycleEventObserver{
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if(event == Lifecycle.Event.ON_PAUSE) {
                    sendWebAssetsStatisticsAnalytics()
                }
            }
        })
    }

    private fun sendWebAssetsStatisticsAnalytics() {
        val webCore = fragmentWebContainer.webCore
        val domainStatistics = webCore.webAssetsStatistics.getDomainStatistics(true)
        domainStatistics.forEach {
            val uri = kotlin.runCatching { webCore.url.toUri() }.getOrNull()
            Analytics.track(
                EventConstants.EVENT_PRELOADING_REPLACE_RESULT,
                mapOf(
                    "failnum" to it.loadErrorCnt,
                    "successnum" to it.loadSuccessCnt,
                    "matchfailnum" to it.loadMissCnt,
                    "domainHost" to it.domain,
                    "iotime" to it.totalIOTime,
                    "webhost" to (uri?.host ?: ""),
                    "webpath" to (uri?.path ?: ""),
                )
            )
        }
    }

    fun onFragmentDestroyed() {

    }

}