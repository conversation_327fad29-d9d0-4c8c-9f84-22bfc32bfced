package com.meta.web.model

import android.os.Parcelable
import androidx.annotation.IntRange
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.util.extension.NavParams
import kotlinx.parcelize.Parcelize

@Parcelize
data class StandaloneWebFragmentArgs(
    override val url: String,
    override val resIdBean: ResIdBean,
    override val extras: TypedMap = TypedMap(),
    @IntRange(-1, 100) override val textZoom: Int = -1,
    val title: String? = null,
    override val from: String? = null,
    val gameId: String? = null,
    val gamePackageName: String? = null,
    val extra: String? = null,
    val isCommunity: Boolean = false,
    val isShowMetaAppShare: Boolean = false,
    val showTitle: Boolean = true,
    val statusBarColor: String? = null,
    val showStatusBar: Boolean = true,
    val showDivider: Boolean = false,
    val dividerColor: Int = -1,
    override val preloadUniqueId: String?,
    override val releaseWhenNavigateUp: Boolean,
    //剪切板分享 scheme 跳转携带
    val shareId: String? = null,
    val categoryId: String? = null,
) : Parcelable, NavParams, BasicWebArgs