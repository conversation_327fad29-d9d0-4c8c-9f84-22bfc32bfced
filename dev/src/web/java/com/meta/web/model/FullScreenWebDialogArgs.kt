package com.meta.web.model

import android.os.Parcelable
import androidx.annotation.IntRange
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.util.extension.NavParams
import kotlinx.parcelize.Parcelize

@Parcelize
data class FullScreenWebDialogArgs(
    override val extras: TypedMap = TypedMap(),
    override val url: String,
    override val resIdBean: ResIdBean,
    @IntRange(-1, 100) override val textZoom: Int = -1,
    override val from: String?,
    override val preloadUniqueId: String?,
    override val releaseWhenNavigateUp: Boolean,
    val messageId: Int,
) : Parcelable, NavParams, BasicWebArgs