package com.meta.web.model

import androidx.annotation.IntRange
import com.socialplay.gpark.function.analytics.resid.ResIdBean

open class BasicWebArgsBuilder(
    val extras: TypedMap = TypedMap(),
    var title: String? = null,
    @IntRange(-1, 100) var textZoom: Int = -1,
    var from: String? = "inner",
    var preloadUniqueId: String? = null,
    var releaseWhenNavigateUp: Boolean = false
)

class StandaloneWebArgsBuilder(
    title: String? = null,
    var showTitle: Boolean = true,
    var statusBarColor: String? = null,
    var showStatusBar: Boolean = true,
    var showDivider: Boolean = false,
    var dividerColor: Int = -1,
    @IntRange(-1, 100) textZoom: Int = -1,
    var categoryId: String? = null,
    var shareId: String? = null,

) : BasicWebArgsBuilder(
    title = title,
    textZoom = textZoom
) {

    fun build(url: String, resIdBean: ResIdBean): StandaloneWebFragmentArgs {
        return StandaloneWebFragmentArgs(
            url = url,
            resIdBean = resIdBean,
            extras = this.extras,
            textZoom = this.textZoom,
            title = this.title,
            from = this.from,
            showTitle = this.showTitle,
            statusBarColor = this.statusBarColor,
            showStatusBar = this.showStatusBar,
            showDivider = this.showDivider,
            dividerColor = this.dividerColor,
            preloadUniqueId = this.preloadUniqueId,
            releaseWhenNavigateUp = this.releaseWhenNavigateUp,
            categoryId = categoryId,
            shareId = shareId,
        )
    }

}


class EmbeddedWebArgsBuilder(
    title: String? = null,
    @IntRange(-1, 100) textZoom: Int = -1,
) : BasicWebArgsBuilder(
    title = title,
    textZoom = textZoom
) {

    fun build(url: String, resIdBean: ResIdBean): EmbeddedWebFragmentArgs {
        return EmbeddedWebFragmentArgs(
            url = url,
            resIdBean = resIdBean,
            extras = this.extras,
            textZoom = this.textZoom,
            title = this.title,
            from = this.from,
            preloadUniqueId = this.preloadUniqueId,
            releaseWhenNavigateUp = this.releaseWhenNavigateUp
        )
    }

}

class WebDialogArgsBuilder(
    @IntRange(-1, 100) textZoom: Int = -1,
    var autoDismiss: Boolean = true
) : BasicWebArgsBuilder(
    textZoom = textZoom
) {

    fun build(url: String, resIdBean: ResIdBean): WebDialogArgs {
        return WebDialogArgs(
            url = url,
            resIdBean = resIdBean,
            extras = this.extras,
            textZoom = this.textZoom,
            from = this.from,
            preloadUniqueId = this.preloadUniqueId,
            releaseWhenNavigateUp = this.releaseWhenNavigateUp,
            autoDismiss = this.autoDismiss
        )
    }
}

class FullScreenWebDialogArgsBuilder(
    @IntRange(-1, 100) textZoom: Int = -1,
    val messageId: Int = 0,
) : BasicWebArgsBuilder(
    textZoom = textZoom
) {
    fun build(url: String, resIdBean: ResIdBean): FullScreenWebDialogArgs {
        return FullScreenWebDialogArgs(
            url = url,
            resIdBean = resIdBean,
            extras = this.extras,
            textZoom = this.textZoom,
            from = this.from,
            preloadUniqueId = this.preloadUniqueId,
            releaseWhenNavigateUp = this.releaseWhenNavigateUp,
            messageId = messageId
        )
    }
}
