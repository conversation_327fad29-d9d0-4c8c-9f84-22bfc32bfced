package com.meta.web.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.IntRange
import com.socialplay.gpark.function.analytics.resid.ResIdBean

interface BasicWebArgs {
    val extras: TypedMap
    val url: String
    val resIdBean: ResIdBean

    @get:IntRange(-1, 100)
    val textZoom: Int
    val from: String?
    val preloadUniqueId: String?
    val releaseWhenNavigateUp: Boolean
}

class TypedValue(
    val type: Int,
    val value: Any?
){
    companion object{
        const val TYPE_STRING = 0
        const val TYPE_INT = 1
        const val TYPE_LONG = 2
        const val TYPE_FLOAT = 3
        const val TYPE_DOUBLE = 4
        const val TYPE_BOOLEAN = 5
    }
}

class TypedMap() :Parcelable {

    companion object CREATOR : Parcelable.Creator<TypedMap> {
        override fun createFromParcel(parcel: Parcel): TypedMap {
            return TypedMap(parcel)
        }

        override fun newArray(size: Int): Array<TypedMap?> {
            return arrayOfNulls(size)
        }
    }

    private val map = mutableMapOf<String, TypedValue>()

    constructor(parcel: Parcel) : this() {
        val size = parcel.readInt()
        for (i in 0 until size) {
            val key = parcel.readString() ?: ""
            val type = parcel.readInt()
            val value = when (type) {
                TypedValue.TYPE_STRING -> parcel.readString()!!
                TypedValue.TYPE_INT -> parcel.readInt()
                TypedValue.TYPE_LONG -> parcel.readLong()
                TypedValue.TYPE_FLOAT -> parcel.readFloat()
                TypedValue.TYPE_DOUBLE -> parcel.readDouble()
                TypedValue.TYPE_BOOLEAN -> parcel.readByte() == 1.toByte()
                else -> throw IllegalArgumentException("Unknown type: $type")
            }
            map[key] = TypedValue(type, value)
        }
    }

    fun putString(key: String, value: String?) {
        map[key] = TypedValue(TypedValue.TYPE_STRING, value)
    }

    fun putInt(key: String, value: Int) {
        map[key] = TypedValue(TypedValue.TYPE_INT, value)
    }

    fun putLong(key: String, value: Long) {
        map[key] = TypedValue(TypedValue.TYPE_LONG, value)
    }

    fun putFloat(key: String, value: Float) {
        map[key] = TypedValue(TypedValue.TYPE_FLOAT, value)
    }

    fun putDouble(key: String, value: Double) {
        map[key] = TypedValue(TypedValue.TYPE_DOUBLE, value)
    }

    fun putBoolean(key: String, value: Boolean) {
        map[key] = TypedValue(TypedValue.TYPE_BOOLEAN, value)
    }

    fun getString(key: String, defaultValue: String? = null): String? {
        val typedValue = map[key] ?: return defaultValue
        return if (typedValue.value is String) {
            typedValue.value
        } else {
            typedValue.value.toString()
        }
    }

    fun getInt(key: String, defaultValue: Int = 0): Int {
        val typedValue = map[key] ?: return defaultValue
        return if (typedValue.value is Number) {
            typedValue.value.toInt()
        } else {
            typedValue.value.toString().toIntOrNull() ?: defaultValue
        }
    }

    fun getLong(key: String, defaultValue: Long = 0L): Long {
        val typedValue = map[key] ?: return defaultValue
        return if (typedValue.value is Number) {
            typedValue.value.toLong()
        } else {
            typedValue.value.toString().toLongOrNull() ?: defaultValue
        }
    }

    fun getFloat(key: String, defaultValue: Float = 0F): Float {
        val typedValue = map[key] ?: return defaultValue
        return if (typedValue.value is Number) {
            typedValue.value.toFloat()
        } else {
            typedValue.value.toString().toFloatOrNull() ?: defaultValue
        }
    }

    fun getDouble(key: String): Double? {
        val typedValue = map[key] ?: return null
        return if (typedValue.value is Number) {
            typedValue.value.toDouble()
        } else {
            typedValue.value.toString().toDoubleOrNull()
        }
    }

    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        val typedValue = map[key] ?: return defaultValue
        return if (typedValue.value is Boolean) {
            typedValue.value
        } else {
            typedValue.value.toString().toBoolean()
        }
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeInt(map.size)
        map.forEach { (key, value) ->
            dest.writeString(key)
            dest.writeInt(value.type)
            when (value.type) {
               TypedValue.TYPE_STRING -> dest.writeString(value.value as? String?)
               TypedValue.TYPE_INT -> dest.writeInt(value.value as Int)
               TypedValue.TYPE_LONG -> dest.writeLong(value.value as Long)
               TypedValue.TYPE_FLOAT -> dest.writeFloat(value.value as Float)
               TypedValue.TYPE_DOUBLE -> dest.writeDouble(value.value as Double)
               TypedValue.TYPE_BOOLEAN -> dest.writeByte(if (value.value as Boolean) 1 else 0)
            }
        }
    }


}