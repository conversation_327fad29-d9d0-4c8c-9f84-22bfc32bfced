package com.meta.web.contract

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.webkit.WebView
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.model.H5RealNameInfo
import com.meta.lib.web.core.model.WebShareParam
import com.meta.web.contract.model.WebShareNativeParams
import com.meta.web.model.BasicWebArgs

interface IWebPlatformContract {
    val debugMode: Boolean
    val embeddedWebContentCacheEnabled: Boolean
    val appPackageName: String
    val appChannelName: String
    val userAgent: String
    val appVersionCode: String

    val webStorage: WebStorage

    fun jumpByScheme(uri: Uri, context: Context): Boolean
    fun jumpByScheme(uri: Uri, fragment: Fragment): Boolean

    fun openLoginPage(fragment: Fragment)
    fun openLoginPage(context: Context)

    fun getUserInfo(): Map<String, Any?>
    fun getBaseParams(): Map<String, Any>
    fun getAbValue(key: String, desc: String, defaultValue: String, valueType: String): Any?

    suspend fun showRealNameAuthDialog(
        fragment: Fragment,
        source: Int,
        realNameInfo: H5RealNameInfo?
    )

    fun isGameInstall(gamePackage: String, installEnvStatus: String, gameId: String?): Boolean

    fun goKF(activity: FragmentActivity)

    /**
     * 从web页面的更多菜单中打开反馈页面
     * @param fragment
     * @param url 点击反馈的页面的url
     */
    fun openFeedbackPage(fragment: Fragment, url: String)

    /**
     * 获取有哪些网站在报错时不需要弹出错误提示
     */
    fun getErrorSuppressedHostList(): Set<String>

    suspend fun share(
        activity: Activity,
        nativeParams: WebShareNativeParams,
        webShareParam: WebShareParam
    )

    fun createLegacyJsApi(
        webCore: WebCore,
        webContainer: Any,
        webView: WebView,
        webArgs: BasicWebArgs
    ): LegacyJsApi

    fun createWebContainerExtension(webCore: WebCore, context: Context, webArgs: BasicWebArgs): WebContainerExtension?

    fun getUrlOptionDefaultValues(): Map<String, String>


    interface WebStorage {
        fun getString(key: String): String
        fun saveString(key: String, value: String)
    }

    interface LegacyJsApi {
        fun exec(json: String?): String?
    }

    interface WebContainerExtension {

        fun onAttachedToWebContainer(containerView: FrameLayout)

        fun onDetachedFromWebContainer(containerView: FrameLayout)
    }

}