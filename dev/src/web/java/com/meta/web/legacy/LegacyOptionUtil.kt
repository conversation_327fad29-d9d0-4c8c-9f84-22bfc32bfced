package com.meta.web.legacy

import com.meta.web.model.EmbeddedWebFragmentArgs
import com.meta.web.model.StandaloneWebFragmentArgs

object LegacyOptionUtil {

    fun toUrlOptions(args: StandaloneWebFragmentArgs): Map<String, String> {
        val map = mutableMapOf<String, String>()

        // 产品说这个参数不需要了，都按照默认参数走

//        map[Url.URL_PARAM_KEY_sys_is_render_title.key] = args.showTitle.toString()
//
//        if (!args.showStatusBar && !args.showTitle) {
//            map[Url.URL_PARAM_KEY_sys_webview_top_position.key] = "T0"
//            map[Url.URL_PARAM_KEY_sys_is_render_title.key] = "false"
//            map[Url.URL_PARAM_KEY_sys_is_render_tabbar.key] = "false"
//        } else if (!args.showTitle) {
//            map[Url.URL_PARAM_KEY_sys_webview_top_position.key] = "T1"
//            map[Url.URL_PARAM_KEY_sys_is_render_title.key] = "false"
//        } else {
//            map[Url.URL_PARAM_KEY_sys_webview_top_position.key] = "T2"
//            map[Url.URL_PARAM_KEY_sys_is_render_title.key] = "true"
//
//        }
//
//        args.statusBarColor?.let {
//            val color = if (it.startsWith("#")) it.substring(1) else it
//            map[Url.URL_PARAM_KEY_sys_tabbar_bg_color.key] = color
//        }
//        args.title?.let { map[Url.URL_PARAM_KEY_sys_title_content.key] = it }

        return map
    }

    fun toUrlOptions(args: EmbeddedWebFragmentArgs): Map<String, String> {
        val map = mutableMapOf<String, String>()
//        args.title?.let { map[Url.URL_PARAM_KEY_sys_title_content.key] }
        return map
    }

}