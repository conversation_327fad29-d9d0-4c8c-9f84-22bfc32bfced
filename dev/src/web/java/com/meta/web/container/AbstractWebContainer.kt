package com.meta.web.container

import android.content.Context
import android.net.Uri
import android.webkit.WebView
import android.widget.FrameLayout
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.container.IWebContainer
import com.meta.pandora.Pandora
import com.meta.pandora.data.entity.Event
import com.meta.web.helper.LoadingViewHelper
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.jsb.AbstractNativeInterface
import com.meta.web.model.BasicWebArgs
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.FileProviderUtil
import java.io.File

abstract class AbstractWebContainer(
    protected val platformContract: IWebPlatformContract,
    protected val context: Context,
    private val webArgs: BasicWebArgs,
) : IWebContainer {

    override val isWebContentsDebuggingEnabled: Boolean get() = BuildConfig.DEBUG

    abstract override fun getNativeInterface(webCore: WebCore): AbstractNativeInterface?

    override val userAgent: String get() = platformContract.userAgent
    abstract val loadingViewHelper: LoadingViewHelper

    private var isLegacyJsApiInitialized: Boolean = false

    private var isContainerExtensionInitialized: Boolean = false
    private var containerExtension: IWebPlatformContract.WebContainerExtension? = null

    override fun getUriForFile(webCore: WebCore, file: File): Uri? {
        return FileProviderUtil.getUriForFile(context, file)
    }

    override fun checkSelfPermission(webCore: WebCore, permission: String): Boolean {
        return PermissionRequest.checkSelfPermission(context, permission)
    }

    override fun sendAnalytics(webCore: WebCore, event: String, params: Map<String, Any>?) {
        Analytics.track(Event(event), params)
    }

    override fun jumpByScheme(webCore: WebCore, uri: Uri): Boolean {
        return platformContract.jumpByScheme(uri, context)
    }

    override fun showLoading(webCore: WebCore) {
        loadingViewHelper.showLoading()
    }

    override fun closeLoading(webCore: WebCore) {
        loadingViewHelper.closeLoading()
    }

    override fun showError(webCore: WebCore, message: String?) {
        loadingViewHelper.showError(message)
    }

    override fun replaceToAvailableWebUrl(webCore: WebCore, url: String, check: Boolean): String {
        if (check) {
            Pandora.Domain.check(url)
        }
        return Pandora.Domain.exchange(url)
    }

    override fun onWebContentLoadFinish(webCore: WebCore, success: Boolean, errorCode: Int, msg: String?) {}

    override fun getErrorSuppressedHostList(webCore: WebCore, ): Set<String> {
        return platformContract.getErrorSuppressedHostList()
    }

    protected abstract fun getWebContainerView(webCore: WebCore, ): FrameLayout

    override fun onAttachWebView(webCore: WebCore, webView: WebView) {
        val layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        val containerView = getWebContainerView(webCore)
        containerView.addView(webView, layoutParams)

        intiWebContainerExtensionIfNeeded(webCore)
        containerExtension?.onAttachedToWebContainer(containerView)

        initLegacyJsApiIfNeeded(webCore, webView)
    }

    override fun onDetachWebView(webCore: WebCore, webView: WebView) {
        val containerView = webView.parent as? FrameLayout?
        containerView?.removeView(webView)
        containerView?.let { containerExtension?.onDetachedFromWebContainer(it) }
    }

    private fun initLegacyJsApiIfNeeded(webCore: WebCore, webView: WebView) {
        if (isLegacyJsApiInitialized) {
            return
        }
        isLegacyJsApiInitialized = true

        val legacyJsApi = createLegacyJsApi(webCore,webView)
        getNativeInterface(webCore)?.setLegacyJsApi(legacyJsApi)
    }

    protected abstract fun createLegacyJsApi(webCore: WebCore, webView: WebView): IWebPlatformContract.LegacyJsApi

    private fun intiWebContainerExtensionIfNeeded(webCore: WebCore) {
        if (isContainerExtensionInitialized) {
            return
        }

        isContainerExtensionInitialized = true
        containerExtension = createWebContainerExtension(webCore)
    }

    protected open fun createWebContainerExtension(webCore: WebCore): IWebPlatformContract.WebContainerExtension? {
        return platformContract.createWebContainerExtension(webCore, context, webArgs)
    }

}