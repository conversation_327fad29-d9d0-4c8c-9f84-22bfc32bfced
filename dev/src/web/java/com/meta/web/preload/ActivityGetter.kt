package com.meta.web.preload

import android.app.Activity
import android.app.Application
import android.os.Bundle
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicReference

internal class ActivityGetter : Application.ActivityLifecycleCallbacks {
    private val topResumedActivityChangedListeners = CopyOnWriteArrayList<TopResumedActivityChangedListener>()

    fun get(): Activity? {
        return activityRef.get()?.takeIf { !it.isDestroyed && !it.isFinishing }
    }

    private val activityRef = AtomicReference<Activity>(null)

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityStarted(activity: Activity) {

    }

    override fun onActivityResumed(activity: Activity) {
        val old = activityRef.get()
        activityRef.set(activity)

        if (old != activity) {
            topResumedActivityChangedListeners.forEach { it.onTopResumedActivityChanged(activity) }
        }
    }

    override fun onActivityPaused(activity: Activity) {

    }

    override fun onActivityStopped(activity: Activity) {

    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivityDestroyed(activity: Activity) {

    }

    fun addTopResumedActivityChangedListener(listener: TopResumedActivityChangedListener) {
        topResumedActivityChangedListeners.add(listener)
    }

    fun removeTopResumedActivityChangedListener(listener: TopResumedActivityChangedListener) {
        topResumedActivityChangedListeners.remove(listener)
    }

    interface TopResumedActivityChangedListener {
        fun onTopResumedActivityChanged(activity: Activity)
    }
}
