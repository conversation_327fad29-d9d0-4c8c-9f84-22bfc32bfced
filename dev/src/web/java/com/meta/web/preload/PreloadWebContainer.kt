package com.meta.web.preload

import android.content.Context
import android.net.Uri
import android.webkit.WebView
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.contract.NativeInterface
import com.meta.lib.web.core.preload.AbsPreloadWebContainer
import com.meta.lib.web.core.preload.PreloadFragment
import com.meta.pandora.Pandora
import com.meta.pandora.data.entity.Event
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.jsb.AbstractNativeInterface
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.util.FileProviderUtil
import java.io.File

internal class PreloadWebContainer(
    val fragment: PreloadFragment,
    val platformContract: IWebPlatformContract,
    val context: Context,
    val args: PreloadWebArgs
) : AbsPreloadWebContainer(fragment) {

    private var preloadNativeInterface: PreloadNativeInterface? = null

    override val isWebContentsDebuggingEnabled: Boolean
        get() {
            val delegatedWebContainer = getDelegatedWebContainer()
            if (delegatedWebContainer != null) {
                return delegatedWebContainer.isWebContentsDebuggingEnabled
            }
            return BuildConfig.DEBUG
        }

    override val userAgent: String
        get() {
            val delegatedWebContainer = getDelegatedWebContainer()
            if (delegatedWebContainer != null) {
                return delegatedWebContainer.userAgent
            }
            return platformContract.userAgent
        }

    private fun getOrCreateNativeInterface(webCore: WebCore): PreloadNativeInterface {
        synchronized(this) {
            if (preloadNativeInterface == null) {
                preloadNativeInterface = PreloadNativeInterface(webCore, platformContract)
            }
        }
        return preloadNativeInterface
            ?: throw IllegalStateException("preloadNativeInterface is null")
    }

    override fun getNativeInterface(webCore: WebCore): NativeInterface {
        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer != null) {
            val nativeInterface = delegatedWebContainer.getNativeInterface(webCore)
            if (nativeInterface is AbstractNativeInterface && nativeInterface.getLegacyJsApi() != null) {
                return nativeInterface
            }
        }

        return getOrCreateNativeInterface(webCore)
    }

    override fun getUriForFile(webCore: WebCore, file: File): Uri? {
        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer != null) {
            return delegatedWebContainer.getUriForFile(webCore, file)
        }
        return FileProviderUtil.getUriForFile(context, file)
    }

    override fun jumpByScheme(webCore: WebCore, uri: Uri): Boolean {
        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer != null) {
            return delegatedWebContainer.jumpByScheme(webCore, uri)
        }

        return platformContract.jumpByScheme(uri, context)
    }

    override fun getErrorSuppressedHostList(webCore: WebCore): Set<String> {

        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer != null) {
            return delegatedWebContainer.getErrorSuppressedHostList(webCore)
        }

        return platformContract.getErrorSuppressedHostList()
    }

    override fun replaceToAvailableWebUrl(
        webCore: WebCore,
        url: String,
        check: Boolean
    ): String {

        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer != null) {
            return delegatedWebContainer.replaceToAvailableWebUrl(webCore, url, check)
        }

        if (check) {
            Pandora.Domain.check(url)
        }

        return Pandora.Domain.exchange(url)
    }


    override fun sendAnalytics(webCore: WebCore, event: String, params: Map<String, Any>?) {
        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer != null) {
            delegatedWebContainer.sendAnalytics(webCore, event, params)
        } else {
            Analytics.track(Event(event), params)
        }
    }

    override fun onAttachWebView(webCore: WebCore, webView: WebView) {
        super.onAttachWebView(webCore, webView)
        val delegatedWebContainer = getDelegatedWebContainer()
        if (delegatedWebContainer == null) {

            val nativeInterface = getOrCreateNativeInterface(webCore)
            if (nativeInterface.legacyJsApi == null) {
                nativeInterface.legacyJsApi =
                    platformContract.createLegacyJsApi(webCore, fragment, webView, args)
            }
        }
    }
}
