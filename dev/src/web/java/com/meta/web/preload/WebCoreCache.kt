package com.meta.web.preload

import android.app.Activity
import android.app.Application
import android.view.ViewGroup
import android.webkit.WebSettings
import android.widget.FrameLayout
import androidx.fragment.app.FragmentActivity
import com.meta.lib.web.core.WebCoreOptions
import com.meta.lib.web.core.WebCoreReleaseMode
import com.meta.lib.web.core.preload.PreloadArgs
import com.meta.lib.web.core.preload.PreloadFragment
import com.meta.web.contract.IWebPlatformContract
import com.socialplay.gpark.R
import org.koin.core.context.GlobalContext
import timber.log.Timber

object WebCoreCache {

    private lateinit var app: Application
    private val TAG = "WebCoreCache"

    private val activityGetter by lazy { ActivityGetter() }

    fun init(app: Application) {
        WebCoreCache.app = app
        app.registerActivityLifecycleCallbacks(activityGetter)
    }

    fun preload(preUnique: String, url: String?) {
        Timber.tag(TAG).d("preload $preUnique $url [PreloadFragment]")
        val platformContract = GlobalContext.get().get<IWebPlatformContract>()
        val fragment = PreloadFragment.newInstance(
            PreloadArgs(
                preloadId = preUnique,
                url = url ?: "",
                webContainerCreator = PreloadContainerCreator::class.java,
                urlOptionsDefaultValues = platformContract.getUrlOptionDefaultValues(),
                options = WebCoreOptions.DEFAULT.copy(
                    cacheMode = WebSettings.LOAD_NO_CACHE,
                    releaseMode = WebCoreReleaseMode.BackgroundRendering
                )
            )
        )

        val activity = activityGetter.get()
        Timber.tag(TAG).d("preload activity:$activity $preUnique $url [PreloadFragment]")
        activityGetter.addTopResumedActivityChangedListener(object : ActivityGetter.TopResumedActivityChangedListener {
            var oldActivity: Activity? = null

            override fun onTopResumedActivityChanged(activity: Activity) {
                Timber.tag(TAG).d("preload->onTopResumedActivityChanged $preUnique $url [PreloadFragment]")
                showFragment(oldActivity, activity, fragment)
            }
        })

        if (activity is FragmentActivity) {
            showFragment(null, activity, fragment)
        }
    }

    private fun showFragment(
        oldActivity: Activity?,
        newActivity: Activity,
        fragment: PreloadFragment
    ) {
        Timber.tag(TAG).d("showFragment oldActivity=%s newActivity=:%s [PreloadFragment]", oldActivity, newActivity)

        if (oldActivity != newActivity && oldActivity is FragmentActivity) {
            oldActivity.supportFragmentManager
                .beginTransaction()
                .remove(fragment)
                .commitNowAllowingStateLoss()
        }

        if (oldActivity != newActivity && newActivity is FragmentActivity) {
            val decorView = newActivity.window.decorView as? ViewGroup ?: return
            var container = decorView.findViewById<FrameLayout>(R.id.preload_web_container)
            if (container == null) {
                container = FrameLayout(newActivity).apply {
                    this.id = R.id.preload_web_container
                }

                decorView.addView(
                    container, 0,
                    ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
            }
            Timber.tag(TAG).d("showFragment decorView container:$container, [PreloadFragment]")

            val preloadFragmentTag = "PreloadFragment"

            val oldFragment = newActivity.supportFragmentManager
                .findFragmentByTag(preloadFragmentTag)

            Timber.tag(TAG).d("showFragment oldFragment:$oldFragment, [PreloadFragment]")

            newActivity.supportFragmentManager
                .beginTransaction().also {
                    if (oldFragment != null) {
                        it.remove(oldFragment)
                    }
                }
                .add(R.id.preload_web_container, fragment, preloadFragmentTag)
                .commitNowAllowingStateLoss()

            Timber.tag(TAG).d("showFragment Fragment add success, oldActivity=%s newActivity=:%s [PreloadFragment]", oldActivity, newActivity)
        }
    }


}