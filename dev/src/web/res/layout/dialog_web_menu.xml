<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="bottom"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="bottom"
        android:layout_width="match_parent"
        android:background="@drawable/bg_web_more_menu_dialog"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_handle_bar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/ic_web_more_menu_handle_bar"
            android:layout_width="wrap_content"
            android:paddingTop="@dimen/dp_6"
            android:paddingHorizontal="@dimen/dp_12"
            android:layout_height="wrap_content"/>

        <LinearLayout
            app:layout_constraintTop_toBottomOf="@id/iv_handle_bar"
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:padding="@dimen/dp_16"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/ll_refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_web_more_menu_refresh" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center"
                    android:text="@string/web_more_menu_refresh"
                    android:textColor="@color/color_212121"
                    android:textSize="@dimen/sp_11" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_feedback"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginStart="@dimen/dp_24"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_web_more_menu_feedback" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center"
                    android:text="@string/web_more_menu_feedback"
                    android:textColor="@color/color_212121"
                    android:textSize="@dimen/sp_11" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_copy_link"
                android:layout_marginStart="@dimen/dp_24"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_web_more_menu_copy_link" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center"
                    android:text="@string/web_more_menu_copy_link"
                    android:textColor="@color/color_212121"
                    android:textSize="@dimen/sp_11" />

            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>