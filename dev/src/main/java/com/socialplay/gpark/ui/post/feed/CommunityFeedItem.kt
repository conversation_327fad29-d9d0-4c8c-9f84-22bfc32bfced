package com.socialplay.gpark.ui.post.feed

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.CarouselModel_
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.post.event.PostMetaData
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.video.PlayerVideoResource
import com.socialplay.gpark.databinding.AdapterItemRoomBinding
import com.socialplay.gpark.databinding.ItemCommunityAssetsCardBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedFootV2Binding
import com.socialplay.gpark.databinding.ItemCommunityFeedHeadBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedImageBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedVideoBinding
import com.socialplay.gpark.databinding.ItemCommunityTopicBlockBinding
import com.socialplay.gpark.databinding.ItemTitleCommunityTopicBinding
import com.socialplay.gpark.function.community.FeedImageVideoUtil
import com.socialplay.gpark.function.post.CommunityUtil
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.CarouselNoSnap
import com.socialplay.gpark.ui.core.views.EpoxyCarouselBuilder
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.carouselBuilder
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.post.PostHelper
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment.Companion.POST_TAG
import com.socialplay.gpark.ui.post.feed.tag.CommunityTagSpannable
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.ui.view.ExpandableTextView.STATE_SHRINK
import com.socialplay.gpark.ui.view.video.VideoPageListView
import com.socialplay.gpark.util.DateUtilWrapper
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.glide.MaxSizeCrop
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.isHttp
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */

data class CommunityFeedVisibleInfo(
    var headVisible: Boolean = false,
    var footVisible: Boolean = false
) {
    fun visible(): Boolean {
        return headVisible || footVisible
    }
}

interface ICommunityTopicListener {
    fun goTopicDetail(tag: PostTag)
    fun trackTopicRankShow(topic: PostTag, rank: Int)
}

interface ICommunityLiveRoomListener : IBaseEpoxyItemListener {
    fun goAllLiveRoom()
    fun showRoomDetail(item: ChatRoomInfo)
    fun trackLiveRoomShow(item: ChatRoomInfo)
}

interface ICommunityFeedListener : IBaseEpoxyItemListener {
    fun calculateVideoPlay(
        resId: String,
        url: String,
        position: Int,
        videoView: VideoPageListView,
        percentVisibleHeight: Float
    )

    fun onItemVisibilityChange(item: CommunityFeedInfo, itemUniqueKey: String, visible: Boolean)

    fun goPost(item: CommunityFeedInfo)
    fun goGameDetail(item: CommunityFeedInfo, gameCard: PostCardInfo)
    fun goProfile(uuid: String)
    fun openImagePreview(url: List<String>, imageViews: List<ImageView>, position: Int) {}
    fun showImageDetail(url: List<String>, position: Int) {}
    fun showVideoDetail(postId: String, url: String)
    fun changeLike(postMetaData: PostMetaData)
    fun showRoomDialog(item: CommunityFeedInfo)
    fun onVideoStateChange(postId: String, resume: Boolean)
    fun goTopic(tag: PostTag, postId: String)
    fun showShareDialog(item: CommunityFeedInfo)
    fun goOutfit(item: CommunityFeedInfo)
    fun goMoment(item: PostMomentCard)
    fun goUgcDesign(item: CommunityFeedInfo)
    fun changeFollow(item: CommunityFeedInfo, uuid: String, follow: Boolean)
    fun clickLabel(data: Pair<Int, LabelInfo?>)
}

private val dp3 by lazy { 3.dp }
private val dp4 by lazy { 4.dp }
private val dp6 by lazy { 6.dp }
private val dp8 by lazy { 8.dp }
private val dp10 by lazy { 10.dp }
private val dp12 by lazy { 12.dp }
private val dp14 by lazy { 14.dp }
private val dp16 by lazy { 16.dp }
private val dp20 by lazy { 20.dp }
private val dp56 by lazy { 56.dp }
private const val ROOM_CARD_RATIO = 190 / 164F

/**
 * 暂时处理滑动recyclerview时，carousel里的item会突然visibleHeigh=0的问题
 */
private class AbsoluteHeightCarouselNoSnapModel(val noSnapHeight: Int) : CarouselModel_() {
    override fun buildView(parent: ViewGroup): CarouselNoSnap {
        val v = CarouselNoSnap(parent.context)
        v.layoutParams =
            ViewGroup.MarginLayoutParams(
                ViewGroup.MarginLayoutParams.MATCH_PARENT,
                noSnapHeight
            )
        return v
    }
}

private class TopicBlockCarouselModel(val absHeight: Int) : CarouselModel_() {

    override fun bind(ca: Carousel) {
        super.bind(ca)
        ca.layoutParams = ViewGroup.MarginLayoutParams(
            ViewGroup.MarginLayoutParams.MATCH_PARENT,
            absHeight
        ).apply {
            marginStart = dp16
            marginEnd = dp16
        }
    }

    override fun buildView(parent: ViewGroup): Grid2VerticalCarousel {
        val v = Grid2VerticalCarousel(parent.context)
        v.apply {
            background = ContextCompat.getDrawable(v.context, R.drawable.bg_topic_square_my_topics)

            layoutParams = ViewGroup.MarginLayoutParams(
                ViewGroup.MarginLayoutParams.MATCH_PARENT,
                absHeight
            ).apply {
                marginStart = dp16
                marginEnd = dp16
            }
        }
        return v
    }
}

class Grid2VerticalCarousel(context: Context) : Carousel(context) {
    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(context, 2, GridLayoutManager.VERTICAL, false)
    }
}

fun MetaEpoxyController.communityTitle(title: String) {
    add(CommunityTitle(title, false, null).id("communityTitle_$title"))
}

/**
 * @param dataList 房间列表
 */
fun MetaEpoxyController.liveRoomBlock(
    title: String,
    dataList: List<ChatRoomInfo>,
    listener: ICommunityLiveRoomListener
) {
    if (dataList.isEmpty()) return
    spacer(height = dp16)
    // 标题
    add(CommunityTitle(title, true, listener).id("TitleCommunityTopic_liveRoomBlock_$title"))
    spacer(height = dp12)
    // liveRooms
    val cardWidth = RoomCardUtil.getRoomCardWidth()
    val cardHeight = (cardWidth * ROOM_CARD_RATIO).toInt()
    carouselBuilder(EpoxyCarouselBuilder(AbsoluteHeightCarouselNoSnapModel(cardHeight))) {
        id("live_room_AbsoluteHeightCarouselNoSnapModel_${title}")
        hasFixedSize(true)
        paddingDp(0)
        spacer(width = dp16, height = cardHeight)
        dataList.forEachIndexed { index, roomInfo ->
            add(
                CommunityFeedFootItem.LiveRoomItem(
                    roomInfo,
                    cardWidth,
                    cardHeight,
                    listener
                ).id("carousel_liveRoomBlock_${roomInfo.roomId}")
            )
            spacer(width = if (index == dataList.lastIndex) dp16 else dp8, height = cardHeight)
        }
    }
}

/**
 * 话题版块
 * @param title 标题
 * @param dataList 话题数据
 * @param listener 事件回调
 */
fun MetaEpoxyController.topicBlock(
    title: String,
    dataList: List<PostTag>,
    listener: ICommunityTopicListener
) {
    if (dataList.isEmpty()) return
    spacer(height = dp16)
    // 标题
    add(CommunityTitle(title, false, null).id("TitleCommunityTopic_topicBlock_$title"))
    spacer(height = dp12)
    // 话题版块
    val line = (dataList.size + 1) / 2
    // itemHeight + itemPadding + bgPadding
    val height = line * dp20 + (line - 1) * dp8 + (dp12 * 2)
    carouselBuilder(EpoxyCarouselBuilder(TopicBlockCarouselModel(height))) {
        id("TopicBlockCarouselModel_${title}")
        padding(Carousel.Padding(dp8, 0))
        dataList.forEachIndexed { index, topic ->
            this.add(
                CommunityTopicBlock(
                    index + 1,
                    topic,
                    listener
                ).id("HotTopicGridCarousel_topicBlock_${title}_${topic.tagId}_${index}")
            )
        }
    }
}

/**
 * 单个帖子
 * @param contentWidth 内容宽度（不包含左右空隙）
 * @param item 帖子数据
 * @param itemPosition 帖子数据排序位置（不是布局）
 * @param showUserStatus 是否展示发帖人用户状态
 * @param showPin 是否展示帖子置顶状态
 * @param firstPaddingTop 第一个帖子的顶部间距
 * @param showTagList 是否展示tag列表
 * @param feedListener 事件回调
 */
@SuppressLint("ClickableViewAccessibility")
fun MetaEpoxyController.communityFeed(
    contentWidth: Int,
    item: CommunityFeedInfo,
    itemPosition: Int,
    showUserStatus: Boolean,
    showPin: Boolean,
    firstPaddingTop: Int,
    fragment: Fragment,
    playLikeAnim: Boolean,
    currentUserId: String,
    feedListener: ICommunityFeedListener
) {
    // 头部
    val uniqueId = item.localUniqueId
    add(
        CommunityFeedHeadItem(
            item,
            contentWidth,
            showUserStatus,
            showPin,
            if (itemPosition == 0) firstPaddingTop else dp14,
            fragment,
            currentUserId,
            feedListener
        ).id("CommunityFeedHeadItem-[$uniqueId]")
    )
    val avatarSpaceWidth = dp56
    if (!item.firstVideo?.resPath.isNullOrEmpty()) {
        // 视频
        add(
            CommunityFeedItemVideo(
                item, uniqueId, contentWidth - avatarSpaceWidth, itemPosition, fragment, feedListener
            ).id("CommunityFeedItem-[$uniqueId]")
        )
    } else if (!item.imageList.isNullOrEmpty()) {
        // 图片
        val totalImages = item.imageList!!.filter { it.resPath.isNotEmpty() }
        val images = totalImages.take(9)

        if (totalImages.isNotEmpty()) {
            if (totalImages.size == 1) {
                // 单图
                add {
                    CommunityFeedItemSingleImage(
                        contentWidth = contentWidth - avatarSpaceWidth,
                        totalImageList = totalImages,
                        imageInfo = totalImages[0],
                        localPublishing = item.localPublishing,
                        position = 0,
                        glide = feedListener.getGlideOrNull(),
                        openImagePreview = {
                            val url = totalImages[0].resPath
                            feedListener.openImagePreview(listOf(url), it, 0)
                        },
                        rootClicked = { feedListener.goPost(item) }
                    ).id("CommunityFeedItemImage-[$uniqueId]-single")
                }
            } else {
                // 多图
                add(
                    CommunityGridImageItem(
                        images = images,
                        contentWidth = contentWidth - avatarSpaceWidth,
                        glide = feedListener.getGlideOrNull(),
                        localPublishing = item.localPublishing,
                        openImagePreview = { imageViews, pos ->
                            feedListener.openImagePreview(images.map { it.resPath }, imageViews, pos)
                        },
                        rootClicked = { feedListener.goPost(item) }
                    ).id("CommunityFeedItemImageRvItem-[$uniqueId]")
                )
            }
        }
    }

    // 最多展示3张卡片
    val cardList = item.cardList.take(3)
    if (cardList.isNotEmpty()) {
        cardList.forEachIndexed { index, card ->
            add {
                CommunityFeedAssetsCard(
                    item,
                    card,
                    feedListener
                ).id("CommunityFeedAssetsCard-${item.postId}-item-$index")
            }
        }
    }

    // 尾部
    add(
        CommunityFeedFootItem(
            item,
            playLikeAnim,
            feedListener
        ).id("CommunityFeedFootItem-[$uniqueId]")
    )
}

// 顶部信息、文本
data class CommunityFeedHeadItem(
    val item: CommunityFeedInfo,
    val contentWidth: Int,
    val showUserStatus: Boolean,
    val showPin: Boolean,
    val paddingTop: Int,
    val fragment: Fragment,
    val currentUserId: String,
    val feedListener: ICommunityFeedListener
) : ViewBindingItemModel<ItemCommunityFeedHeadBinding>(
    R.layout.item_community_feed_head,
    ItemCommunityFeedHeadBinding::bind
) {
    override fun ItemCommunityFeedHeadBinding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        vMask.isVisible = item.localPublishing

        // 当前是主态或者关注中的状态, 就隐藏关注按钮
        if (item.uid == currentUserId || item.followStatus == true) {
            // 主态
            ivFollow.invisible()
            if (!lavFollowAnim.isAnimating) {
                lavFollowAnim.gone()
            }
            vFollowClick.gone()
            vFollowClick.unsetOnClick()
        } else {
            // 客态
            lavFollowAnim.setMinAndMaxProgress(0.0f, 1.0f)
            ivFollow.visible()
            lavFollowAnim.cancelAnimationIfAnimating()
            lavFollowAnim.gone()
            vFollowClick.visible()
            vFollowClick.setOnAntiViolenceClickListener {
                feedListener.changeFollow(
                    item,
                    item.uid,
                    true
                )
                vFollowClick.gone()
                ivFollow.invisible()
                lavFollowAnim.visible()
                lavFollowAnim.progress = 0f
                lavFollowAnim.playAnimation()
            }
        }
        tvName.text = item.nickname
        ivPortrait.setMargin(top = paddingTop)
        feedListener.getGlideOrNull()?.run {
            load(item.avatar).placeholder(R.drawable.icon_default_avatar)
                .into(ivPortrait)
        }
        tvTime.text = DateUtilWrapper.getCreateFormatDate(context, item.createTime)
        val content = CommunityUtil.parsePostContent(
            root.context,
            item.content,
            item.tagList,
            item.communityTagList,
        ) { tag ->
            Timber.tag(POST_TAG).d("tvContent spannable")
            feedListener.goTopic(tag, item.postId)
        }
        if (content.isNullOrEmpty()) {
            tvContent.gone()
        } else {
            tvContent.visible()
            tvContent.setOnShrinkingCallback { start ->
                val tempSetPre = HashSet<String>()
                val tempSetPost = HashSet<String>()
                item.communityTagList?.forEach {
                    if (it.index + it.tagName.length < start) {
                        tempSetPre.add(it.tagName)
                    } else if (!tempSetPre.contains(it.tagName)) {
                        tempSetPost.add(it.tagName)
                    }
                }
                val filteredTags = item.tagList?.filter {
                    tempSetPost.contains(it.tagName)
                }
                if (filteredTags.isNullOrEmpty()) {
                    null
                } else {
                    parseTagList(
                        root.context,
                        filteredTags,
                        "\n"
                    ) { tag ->
                        feedListener.goTopic(tag, item.postId)
                    }
                }
            }
            tvContent.updateForRecyclerView(
                content,
                contentWidth,
                STATE_SHRINK
            )
        }
        ivCertification.show(
            item.tagIds,
            item.labelInfo ?: item.user?.labelInfo,
            isOfficial = item.user?.isOfficial == true,
            glide = feedListener.getGlideOrNull()
        )
        PostHelper.updateReviewStatusForIcon(tvTime, item.status)
        val gameStatus = item.userStatus.toLocalStatus()
        when {
            !showUserStatus -> {
                ivState.isVisible = false
            }

            item.canGoRoom && item.userStatus != null -> {
                ivState.isVisible = true
            }

            gameStatus == FriendStatus.ONLINE || gameStatus == FriendStatus.PLAYING_GAME -> {
                ivState.isVisible = true
            }

            else -> {
                ivState.isVisible = false
            }
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        tvContent.setOnAntiViolenceClickListener {
            Timber.tag(POST_TAG).d("tvContent onClick")
            feedListener.goPost(item)
        }
        layerUser.setOnAntiViolenceClickListener {
            if (item.canGoRoom && showUserStatus) {
                feedListener.showRoomDialog(item)
            } else {
                feedListener.goProfile(item.uid)
            }
        }
        ivCertification.setListener {
            feedListener.clickLabel(it)
        }
    }

    override fun ItemCommunityFeedHeadBinding.onUnbind() {
        ivCertification.setListener(null)
        vFollowClick.unsetOnClick()
        root.unsetOnClick()
        tvContent.unsetOnClick()
        layerUser.unsetOnClick()
    }

    private fun parseTagList(
        context: Context,
        tagList: List<PostTag>?,
        prefix: CharSequence?,
        tagClickCallback: (PostTag) -> Unit
    ): CharSequence? {
        if (tagList.isNullOrEmpty()) return null
        val builder = SpannableHelper.Builder()
        if (!prefix.isNullOrEmpty()) {
            builder.text(prefix)
        }
        // 去重
        tagList.distinctBy { it.tagId }.forEachIndexed { index, postTag ->
            builder.text(context.getString(R.string.community_hashtag_prefix, postTag.tagName))
                .click(CommunityTagSpannable(context, postTag, tagClickCallback))
            // 防止点击spannable后面空白区域也会触发link click，文本最后加了一个空格
            builder.text(" ")
        }
        return builder.build()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onItemVisibilityChange(
                item,
                "head",
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }
}

/**
 * 话题版块-单个话题
 * @param rank 排序，从1开始
 * @param topic 话题数据
 * @param listener 事件回调
 */
data class CommunityTopicBlock(
    val rank: Int,
    val topic: PostTag,
    val listener: ICommunityTopicListener
) : ViewBindingItemModel<ItemCommunityTopicBlockBinding>(
    R.layout.item_community_topic_block,
    ItemCommunityTopicBlockBinding::bind
) {
    override fun ItemCommunityTopicBlockBinding.onBind() {
        tvTopicFire.visible(topic.hot)
        tvTopic.setTextWithArgs(R.string.community_hashtag_prefix, topic.tagName)
        tvTopic.setOnAntiViolenceClickListener {
            listener.goTopicDetail(topic.buildPostTag())
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.trackTopicRankShow(topic, rank)
        }
    }

    override fun ItemCommunityTopicBlockBinding.onUnbind() {
        tvTopic.unsetOnClick()
    }
}

/**
 * 话题版块标题
 */
data class CommunityTitle(
    val title: String,
    val showSeeAll: Boolean,
    val listener: ICommunityLiveRoomListener?
) : ViewBindingItemModel<ItemTitleCommunityTopicBinding>(
    R.layout.item_title_community_topic,
    ItemTitleCommunityTopicBinding::bind
) {
    override fun ItemTitleCommunityTopicBinding.onBind() {
        tvTitle.text = title
        tvMore.isVisible = showSeeAll
        if (showSeeAll && listener != null) {
            tvMore.setOnAntiViolenceClickListener {
                listener.goAllLiveRoom()
            }
        }
    }

    override fun ItemTitleCommunityTopicBinding.onUnbind() {
        tvMore.unsetOnClick()
    }
}

// 视频
data class CommunityFeedItemVideo(
    val item: CommunityFeedInfo,
    val uniqueId: String,
    val contentWidth: Int,
    val position: Int,
    val fragment: Fragment,
    val feedListener: ICommunityFeedListener
) : ViewBindingItemModel<ItemCommunityFeedVideoBinding>(
    R.layout.item_community_feed_video,
    ItemCommunityFeedVideoBinding::bind
) {
    override fun ItemCommunityFeedVideoBinding.onBind() {
        root.children.forEach {
            it.enableWithAlpha(!item.localPublishing, 0.5F)
        }
        vMask.isVisible = item.localPublishing

        val videoInfo = item.firstVideo!!
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        pageListView.apply {
            val videoPath = videoInfo.resPath
            setDataResource(PlayerVideoResource(videoPath, ""))
            currentFragment = fragment
            FeedImageVideoUtil.setVideoSize(
                spaceNotReady,
                pageListView,
                videoInfo,
                videoPath,
                contentWidth,
                feedListener.getGlideOrNull()
            )
            setOnClickFullScreen {
                feedListener.showVideoDetail(item.postId, videoPath)
            }
            setOnVideoResume {
                feedListener.onVideoStateChange(item.postId, true)
            }
            setOnVideoPause {
                feedListener.onVideoStateChange(item.postId, false)
            }
        }
    }

    override fun onVisibilityChanged(
        percentVisibleHeight: Float,
        percentVisibleWidth: Float,
        visibleHeight: Int,
        visibleWidth: Int,
        view: View
    ) {
        super.onVisibilityChanged(
            percentVisibleHeight,
            percentVisibleWidth,
            visibleHeight,
            visibleWidth,
            view
        )
        feedListener.calculateVideoPlay(
            uniqueId,
            item.firstVideo!!.resPath,
            position,
            view.findViewById(R.id.pageListView),
            percentVisibleHeight
        )
    }

    override fun ItemCommunityFeedVideoBinding.onUnbind() {
        root.unsetOnClick()
    }
}

// 图片列表
data class CommunityFeedItemImage(
    val contentWidth: Int,
    val totalImageList: List<PostMediaResource>,
    val imageList: List<PostMediaResource>,
    val localPublishing: Boolean,
    val position: Int,
    val actualPosition: Int,
    val feedListener: ICommunityFeedListener
) : ViewBindingItemModel<ItemCommunityFeedImageBinding>(
    R.layout.item_community_feed_image,
    ItemCommunityFeedImageBinding::bind
) {
    override fun ItemCommunityFeedImageBinding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!localPublishing, 0.5F)
        }
        vMask.isVisible = localPublishing

        val showSpaceStart = position == 0
        val showSpaceEnd = position == imageList.lastIndex
        val imageInfo = imageList[position]
        // 优先使用缩略图
        val imagePath = imageInfo.thumb.ifNullOrEmpty { imageInfo.resPath }
        val isLongLongImage = imageInfo.resourceHeight > 0
                && imageInfo.resourceWidth > 0
                && imageInfo.resourceHeight / imageInfo.resourceWidth >= 3
        if (totalImageList.size > 1) {
            // 多图
            FeedImageVideoUtil.scaleImageList(
                showSpaceStart,
                showSpaceEnd,
                ivImage,
                88.dp,
                dp3,
                dp16,
                dp16,
            )
            feedListener.getGlideOrNull()?.run {
                val glide = if (imagePath.isHttp()) {
                    load(imagePath)
                } else {
                    load(imagePath).skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.placeholder(R.drawable.placeholder_corner_6)

                if (isLongLongImage) {
                    glide.transform(MaxSizeCrop(maxHeight = ivImage.layoutParams.height))
                } else {
                    glide.centerCrop()
                }.into(ivImage)
            }
        } else if (imageInfo.resourceWidth > 0 && imageInfo.resourceHeight > 0) {
            // 单图
            FeedImageVideoUtil.scaleImageSingle(
                showSpaceStart,
                showSpaceEnd,
                ivImage,
                contentWidth,
                imageInfo.resourceWidth,
                imageInfo.resourceHeight,
                dp16,
                dp16
            )
            feedListener.getGlideOrNull()?.run {
                val glide = if (imagePath.isHttp()) {
                    load(imagePath)
                } else {
                    load(imagePath).skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.placeholder(R.drawable.placeholder_corner_6)

                if (isLongLongImage) {
                    glide.transform(MaxSizeCrop(maxHeight = ivImage.layoutParams.height))
                } else {
                    glide.centerCrop()
                }.into(ivImage)
            }
        } else {
            feedListener.getGlideOrNull()?.run {
                // 没有宽高的图
                if (imagePath.isHttp()) {
                    asBitmap()
                } else {
                    asBitmap().skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.load(imagePath).into(object : SimpleTarget<Bitmap?>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap?>?
                    ) {
                        FeedImageVideoUtil.scaleImageSingle(
                            showSpaceStart,
                            showSpaceEnd,
                            ivImage,
                            contentWidth,
                            resource.width,
                            resource.height,
                            dp16,
                            dp16
                        )
                        ivImage.setImageBitmap(resource)
                    }
                })
            }
        }
        ivImage.setOnAntiViolenceClickListener {
            //feedListener.showImageDetail(totalImageList.map { it.resPath }, actualPosition)
        }
    }
}

data class CommunityFeedAssetsCard(
    val item: CommunityFeedInfo,
    /**
     * item 的类型可能有以下类型:
     * 游戏卡片: PostCardInfo
     * 拍剧卡片: PostMomentCard
     * 穿搭卡片: PostStyleCard
     * 服装卡片: PostUgcDesignCard
     */
    val card: Any,
    val feedListener: ICommunityFeedListener
) : ViewBindingItemModel<ItemCommunityAssetsCardBinding>(
    R.layout.item_community_assets_card,
    ItemCommunityAssetsCardBinding::bind
) {
    override fun ItemCommunityAssetsCardBinding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        ivDelBtn.gone()
        val defaultBgColor = getColorByRes(R.color.color_97BCDE)
        if (card is PostCardInfo) {
            // 游戏卡片
            layoutMetaLike.visible()
            mlvLike.visible()
            mlvPlayers.visible()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.gameIcon).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.gameName
            mlvLike.setLikeText(UnitUtil.formatKMCount(card.likeCount))
            mlvPlayers.setLikeText(UnitUtil.formatKMCount(card.player))
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goGameDetail(item, card)
            }
        } else if (card is PostMomentCard) {
            // 拍剧卡片
            layoutMetaLike.gone()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.materialUrl).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.templateName
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goMoment(card)
            }
        } else if (card is PostStyleCard) {
            // 穿搭卡片
            layoutMetaLike.visible()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.visible()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.wholeBodyImage).centerCrop()
                    .placeholder(R.drawable.avatar_friend_placeholder)
                    .into(ivIcon)
            }
            tvTitle.setTextWithArgs(
                R.string.s_outfit,
                item.nickname.orEmpty()
            )
            mlvTryOn.setLikeText(getString(R.string.tried_on, card.pvStr))
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goOutfit(item)
            }
        } else if (card is PostUgcDesignCard) {
            // 服装卡片
            layoutMetaLike.visible()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.visible()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.cover).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.title.ifNullOrEmpty { getString(R.string.fashion_design) }
            mlvTryOn.setLikeText(getString(R.string.tried_on, card.tryOnCount ?: 0))
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goUgcDesign(item)
            }
        }
    }

    override fun ItemCommunityAssetsCardBinding.onUnbind() {
        root.unsetOnClick()
        bgCard.unsetOnClick()
    }
}

// 底部卡片、tag、态度、分割线
data class CommunityFeedFootItem(
    val item: CommunityFeedInfo,
    val playLikeAnim: Boolean,
    val feedListener: ICommunityFeedListener
) : ViewBindingItemModel<ItemCommunityFeedFootV2Binding>(
    R.layout.item_community_feed_foot_v2,
    ItemCommunityFeedFootV2Binding::bind
) {

    override fun ItemCommunityFeedFootV2Binding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        vMask.isVisible = item.localPublishing
        // 没有置顶评论, 所以这里始终显示分割线
        vLine.visible(true)

        // attitude
        includeAttitude.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)
        if (item.opinionLike) {
            includeAttitude.ivLike.invisible()
            includeAttitude.lavLikeAnim.visible()
            if (!includeAttitude.lavLikeAnim.isAnimating) {
                includeAttitude.lavLikeAnim.progress = 1f
            }
        } else {
            includeAttitude.ivLike.visible()
            includeAttitude.lavLikeAnim.cancelAnimationIfAnimating()
            includeAttitude.lavLikeAnim.gone()
        }
        includeAttitude.tvLike.text = UnitUtil.formatKMCount(item.likeCount ?: 0)
        includeAttitude.tvLike.setTextColorByRes(if (item.opinionLike) R.color.color_FF5F42 else R.color.color_666666)
        includeAttitude.tvComment.text = UnitUtil.formatKMCount(item.commentCount ?: 0)
        includeAttitude.tvShare.text = UnitUtil.formatKMCount(item.shareCount ?: 0)
        includeAttitude.layerLike.setOnAntiViolenceClickListener {
            if (!item.postId.isNullOrEmpty()) {
                feedListener.changeLike(item.getPostMetaData())
                val targetLike = !item.opinionLike
                if (targetLike) {
                    includeAttitude.ivLike.invisible()
                    includeAttitude.lavLikeAnim.visible()
                    includeAttitude.lavLikeAnim.progress = 0f
                    includeAttitude.lavLikeAnim.playAnimation()
                } else {
                    includeAttitude.ivLike.visible()
                    includeAttitude.lavLikeAnim.gone()
                }
            }
        }
        includeAttitude.layerComment.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        includeAttitude.layerShare.setOnAntiViolenceClickListener {
            if (item.reviewFail) {
                getItemView().context.toast(R.string.post_review_failed_share_tip)
            } else if (item.reviewInProgress) {
                getItemView().context.toast(R.string.post_under_review_share_tip)
            } else if (item.reviewOk) {
                feedListener.showShareDialog(item)
            }
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
    }

    override fun ItemCommunityFeedFootV2Binding.onUnbind() {
        includeAttitude.lavLikeAnim.cancelAnimation()
        includeAttitude.lavLikeAnim.progress = 0f
        root.unsetOnClick()
        includeAttitude.layerLike.unsetOnClick()
        includeAttitude.layerComment.unsetOnClick()
        includeAttitude.layerShare.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onItemVisibilityChange(
                item,
                "foot",
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }

    /**
     * @param roomInfo 房间信息
     * @param cardWidth 卡片宽度
     * @param listener 操作回调
     */
    data class LiveRoomItem(
        val roomInfo: ChatRoomInfo,
        val cardWidth: Int,
        val cardHeight: Int,
        val listener: ICommunityLiveRoomListener
    ) : ViewBindingItemModel<AdapterItemRoomBinding>(
        R.layout.adapter_item_room,
        AdapterItemRoomBinding::bind
    ) {

        override fun AdapterItemRoomBinding.onBind() {
            RoomCardUtil.setImageBg(listener.getGlideOrNull(), this, roomInfo)
            RoomCardUtil.setRoomName(false, cardWidth, root.context, this, roomInfo)
            RoomCardUtil.setRoomTag(root.context, this, roomInfo)
            RoomCardUtil.setRoomPlayerCount(root.context, this, roomInfo)
            RoomCardUtil.setRoomMember(listener.getGlideOrNull(), this, roomInfo)
            RoomCardUtil.setRoomStyle(this, roomInfo)
            root.setOnAntiViolenceClickListener {
                listener.showRoomDetail(roomInfo)
            }
        }

        override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
            super.onVisibilityStateChanged(visibilityState, view)
            Timber.tag(POST_TAG)
                .d("onVisibilityStateChanged name: ${roomInfo.roomName}, visibilityState: ${visibilityState}")
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.trackLiveRoomShow(roomInfo)
            }
        }

        override fun createView(parent: ViewGroup): View {
            return super.createView(parent).apply {
                findViewById<ConstraintLayout>(R.id.cl_content).setSize(cardWidth, cardHeight)
            }
        }
    }
}