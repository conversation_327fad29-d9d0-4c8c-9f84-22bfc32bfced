package com.socialplay.gpark.ui.outfit

import android.content.Context
import android.graphics.Color
import android.graphics.Outline
import android.os.SystemClock
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.view.setPadding
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcAssetNotice
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.databinding.ItemGameDetailOperationBinding
import com.socialplay.gpark.databinding.ItemUgcAssetFeedNoticesBinding
import com.socialplay.gpark.databinding.ItemUgcAssetTagBinding
import com.socialplay.gpark.databinding.ItemUgcDesignFeedBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.ViewItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.view.FlowLayout
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.ui.view.SquareImageView
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.backgroundTintListByColor
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.setBackgroundColorByRes
import com.socialplay.gpark.util.extension.setFontWeight400
import com.socialplay.gpark.util.extension.setFontWeight500
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.unsetOnLongClickAndLongClickable
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */

interface IUgcDesignFeedListener : IBaseEpoxyItemListener {
    fun clickOutfit(item: UgcDesignFeed, position: Int)
    fun clickLike(item: UgcDesignFeed, position: Int)
    fun more(item: UgcDesignFeed, position: Int)
    fun showDesign(item: UgcDesignFeed, position: Int)
    fun clickOperation(item: UgcAssetNotice, position: Int) {}
}

fun MetaEpoxyController.ugcDesignFeedItem(
    item: UgcDesignFeed,
    position: Int,
    uuid: String,
    uniqueTag: Int,
    prefix: String,
    id: String? = null,
    listener: IUgcDesignFeedListener
) {
    // 若商业化开关关闭, 且商品已定价, 需隐藏定价商品
    if(!PandoraToggle.enableAssetCommercialization && item.isPriced == true) return
    add(
        UgcDesignFeedItem(
            item,
            position,
            uuid,
            listener
        ).id(id ?: "${prefix}-${uniqueTag}-${item.feedId}")
    )
}

data class UgcDesignFeedItem(
    val item: UgcDesignFeed,
    val position: Int,
    val uuid: String,
    val listener: IUgcDesignFeedListener
) : ViewBindingItemModel<ItemUgcDesignFeedBinding>(
    R.layout.item_ugc_design_feed,
    ItemUgcDesignFeedBinding::bind
) {

    override fun ItemUgcDesignFeedBinding.onBind() {
        if (item.isUgcModule) {
            ivOutfit.setPadding(0)
            listener.getGlideOrNull()?.run {
                load(item.cover)
                    .centerCrop()
                    .into(ivOutfit)
            }
        } else {
            ivOutfit.setPadding(dp(23))
            listener.getGlideOrNull()?.run {
                load(item.cover)
                    .fitCenter()
                    .into(ivOutfit)
            }
        }
        tvTitle.goneIfValueEmpty(item.title)
        flTags.removeAllViews()
        flTags.visible(!item.tags.isNullOrEmpty())
        item.tags?.take(3)?.forEach {
            val b = root.createViewBinding(ItemUgcAssetTagBinding::inflate)
            b.root.text = it
            flTags.addView(b.root)
        }
        if (item.enablePrice) {
            tvPrice.visible()
            tvPrice.text = if(item.isPriced == true) {
                UnitUtil.numWithComma(item.price ?: 0)
            } else {
                getString(R.string.cloth_price_free)
            }
        } else {
            tvPrice.gone()
        }
        listener.getGlideOrNull()?.run {
            load(item.userIcon)
                .placeholder(R.drawable.placeholder_circle)
                .circleCrop()
                .into(ivAvatar)
        }
        tvUsername.text = item.userName
        tvLikeCount.text = UnitUtil.formatKMCount(item.favorites)
        tvLikeCount.isSelected = item.isFavorite

        root.setOnAntiViolenceClickListener {
            listener.clickOutfit(item, position)
        }
        if (item.uuid == uuid) {
            root.unsetOnLongClickAndLongClickable()
        } else {
            root.setOnLongClickListener {
                listener.more(item, position)
                true
            }
        }
        tvLikeCount.setOnAntiViolenceClickListener {
            listener.clickLike(item, position)
        }
    }

    override fun ItemUgcDesignFeedBinding.onUnbind() {
        root.unsetOnClick()
        root.unsetOnLongClickAndLongClickable()
        tvLikeCount.unsetOnClick()
        flTags.removeAllViews()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.showDesign(item, position)
        }
    }
}

fun MetaEpoxyController.ugcAssetFeedNoticesItem(
    items: List<UgcAssetNotice>,
    spanSize: Int,
    listener: IUgcDesignFeedListener
) {
    add(
        UgcAssetFeedNoticesItem(
            items,
            spanSize,
            listener
        ).id("UgcAssetFeedNotices").spanSizeOverride { totalSpanCount, _, _ ->
            spanSize.coerceAtMost(totalSpanCount)
        }
    )
}

data class UgcAssetFeedNoticesItem(
    val items: List<UgcAssetNotice>,
    val spanSize: Int,
    val listener: IUgcDesignFeedListener
) : ViewBindingItemModel<ItemUgcAssetFeedNoticesBinding>(
    R.layout.item_ugc_asset_feed_notices,
    ItemUgcAssetFeedNoticesBinding::bind
) {

    override fun ItemUgcAssetFeedNoticesBinding.onBind() {
        if (root.layoutParams is StaggeredGridLayoutManager.LayoutParams) {
            root.layoutParams =
                (root.layoutParams as? StaggeredGridLayoutManager.LayoutParams)?.apply {
                    isFullSpan = true
                }
        }

        includeOp1.bindOperation(items.getOrNull(0), 0)
        includeOp2.bindOperation(items.getOrNull(1), 1)
        includeOp3.bindOperation(items.getOrNull(2), 2)
    }

    override fun ItemUgcAssetFeedNoticesBinding.onUnbind() {
        includeOp1.root.unsetOnClick()
        includeOp2.root.unsetOnClick()
        includeOp3.root.unsetOnClick()
    }

    private fun ItemGameDetailOperationBinding.bindOperation(item: UgcAssetNotice?, position: Int) {
        if (item == null) {
            root.gone()
            return
        }
        root.visible()
        if (item.tagName.isNullOrEmpty()) {
            tvTag.gone()
        } else {
            tvTag.visible()
            val color = kotlin.runCatching {
                item.tagColor?.let {
                    Color.parseColor(it)
                } ?: getColorByRes(R.color.color_4AB4FF)
            }.getOrDefault(getColorByRes(R.color.color_4AB4FF))
            tvTag.text = item.tagName
            tvTag.backgroundTintListByColor(color)
        }
        tvTitle.text = item.noticeTitle
        root.setOnAntiViolenceClickListener {
            listener.clickOperation(item, position)
        }
    }
}