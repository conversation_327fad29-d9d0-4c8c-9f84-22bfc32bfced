package com.socialplay.gpark.ui.profile.fans

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.data.model.profile.RelationListResult.RelationUserInfo.Companion.TYPE_AI_BOT
import com.socialplay.gpark.databinding.FragmentUserFansItemBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.simpleController
import kotlinx.parcelize.Parcelize
import com.socialplay.gpark.ui.core.views.loadMoreFooter

/**
 * Created by bo.li
 * Date: 2023/9/20
 * Desc:
 */
@Parcelize
data class UserFansItemFragmentArgs(val uuid: String, val type: String) : Parcelable

class UserFansItemFragment :
    BaseRecyclerViewFragment<FragmentUserFansItemBinding>(R.layout.fragment_user_fans_item) {

    companion object {
        const val TYPE_FOLLOWING = "following"
        const val TYPE_FANS = "fans"
        const val TYPE_FRIEND= "friend"
        fun newInstance(type: String, otherUuid: String) = UserFansItemFragment().apply {
            arguments = UserFansItemFragmentArgs(otherUuid, type).asMavericksArgs()
        }
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvUserFans

    private val viewModel: UserFansItemViewModel by fragmentViewModel()
    private val args by args<UserFansItemFragmentArgs>()

    private val itemListener = object : IUserRelationTabListener {
        override fun getGlideOrNull(): RequestManager? {
            return glide
        }

        override fun click(item: RelationListResult.RelationUserInfo) {
            if (item.type == TYPE_AI_BOT) {
                MetaRouter.AiBot.gotoAiBotDetail(
                    this@UserFansItemFragment,
                    item.userNumber ?: "",
                    item.uuid,
                    "3"
                )
            } else {
                MetaRouter.Profile.other(this@UserFansItemFragment, item.uuid, "fans")
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUserFansItemBinding? {
        return FragmentUserFansItemBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        viewModel.registerAsyncErrorToast(UserFansItemModelState::refresh)
        viewModel.setupRefreshLoading(
            UserFansItemModelState::refresh,
            binding.loading,
            binding.refresh,
        ) {
            viewModel.refreshList()
        }
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        UserFansItemModelState::list,
        UserFansItemModelState::loadMore
    ) { list, loadMore ->
        list.forEach { item ->
            followerFansItem(item, itemListener)
        }
        if (list.isNotEmpty()) {
            loadMoreFooter(
                loadMore,
                idStr = "UserRelationTabFooter-${args.type}",
                showEnd = true
            ) {
                viewModel.loadMoreList()
            }
        }
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_USER_FANS_ITEM

    override fun isEnableTrackPageExposure(): Boolean = false
}