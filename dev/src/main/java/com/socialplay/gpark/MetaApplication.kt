package com.socialplay.gpark

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.paging.ExperimentalPagingApi
import com.bumptech.glide.Glide
import com.socialplay.gpark.app.ApplicationLifeCycle
import com.socialplay.gpark.app.MetaApplicationLifecycle
import com.socialplay.gpark.function.analytics.handle.AppLaunchAnalytics
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.ui.main.HomeImageShowAnalytics
import com.socialplay.gpark.util.GlideCrashFix
import com.socialplay.gpark.util.GlideExceptionHandler
import com.socialplay.gpark.util.GlideRecycledBitmapFix
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/09
 * desc   :
 * </pre>
 */


@ExperimentalPagingApi
class MetaApplication : Application() {

    private lateinit var applicationLifecycle: ApplicationLifeCycle

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(MetaLanguages.attachContext(base))

        // 尽早安装Glide异常处理器
        GlideExceptionHandler.install()

        HomeImageShowAnalytics.startBootTime = System.currentTimeMillis()
        AppLaunchAnalytics.handleAppOnAttachBefore()
        applicationLifecycle = MetaApplicationLifecycle(this)
        applicationLifecycle.attachContext()
        AppLaunchAnalytics.handleAppOnAttachAfter()
    }

    override fun onCreate() {
        AppLaunchAnalytics.handleAppOnCreateBefore()
        super.onCreate()
        applicationLifecycle.onCreate()

        // 应用Glide回收Bitmap崩溃修复
        try {
            GlideRecycledBitmapFix.applyFix()
            Timber.d("Glide recycled bitmap fix applied: ${GlideRecycledBitmapFix.getBitmapPoolInfo()}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to apply Glide recycled bitmap fix")
        }

        AppLaunchAnalytics.handleAppOnCreateAfter()
    }

    override fun onTerminate() {
        super.onTerminate()
        applicationLifecycle.onTerminate()
    }

    override fun onTrimMemory(level: Int) {
        try {
            // 使用GlideCrashFix安全地处理内存清理
            Timber.d("onTrimMemory called with level: $level")
            Timber.d("BitmapPool status before trim: ${GlideCrashFix.getBitmapPoolStatus()}")

            // 安全地调用Glide的trimMemory
            GlideCrashFix.safeTrimGlideMemory(level)

            Timber.d("BitmapPool status after trim: ${GlideCrashFix.getBitmapPoolStatus()}")

            // 调用父类的onTrimMemory，但跳过Glide的处理（因为我们已经安全处理了）
            // 这里我们不调用super.onTrimMemory()来避免重复处理Glide
        } catch (e: Exception) {
            Timber.e(e, "Error in onTrimMemory")
            // 如果出现任何异常，仍然尝试调用父类方法
            try {
                super.onTrimMemory(level)
            } catch (superException: Exception) {
                Timber.e(superException, "Error in super.onTrimMemory")
            }
        }
    }
}