package com.socialplay.gpark.function.analytics

import android.content.Context
import android.os.Bundle
import com.socialplay.gpark.util.GsonUtil
import com.meta.pandora.data.entity.Event
import com.meta.pandora.utils.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch

class OtherProcessEventSender(private val context: Context) : CoroutineScope by CoroutineScope(Dispatchers.IO) {
    private val eventChannel = Channel<Triple<Int, Event, Map<String, Any>?>>(Channel.UNLIMITED)

    companion object {
        private const val RETRY_COUNT = 3
        private const val RETRY_MAX_COUNT = 100
        private const val RETRY_IMMEDIATELY_TIME = 100L
        private const val RETRY_LATER_TIME = 1000L
    }

    init {
        launch {
            eventChannel.consumeAsFlow().collect(::sendToMainProcess)
        }
    }

    fun send(event: Event, params: Map<String, Any>? = null) = launch {
        eventChannel.send(Triple(0, event, params))
    }

    private suspend fun sendToMainProcess(data: Triple<Int, Event, Map<String, Any>?>) {
        val event = data.second
        val params = data.third
        val uri = AnalyticsContentProvider.getEventUri(context)
        val json = GsonUtil.gson.toJson(MultiProcessEvent(event, params))
        val extra = Bundle()
        extra.putString(AnalyticsContentProvider.EVENT_VALUE, json)

        for (i in 0 until RETRY_COUNT) {
            try {
                context.contentResolver.call(uri, AnalyticsContentProvider.METHOD_EVENT, null, extra)
                break
            } catch (e: Exception) {
                Log.e(e)
                if (i < RETRY_COUNT - 1) {
                    Log.e { "send $event to main process error,retry immediately" }
                    delay(RETRY_IMMEDIATELY_TIME)
                } else if (data.first < RETRY_MAX_COUNT) {
                    Log.e { "send $event to main process error,retry later" }
                    delay(RETRY_LATER_TIME)
                    eventChannel.trySend(Triple(data.first + 1, event, params))
                } else {
                    throw e
                }
            }
        }
    }
}