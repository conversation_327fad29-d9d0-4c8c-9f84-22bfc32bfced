package com.socialplay.gpark.function.community

import android.graphics.Bitmap
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ImageView.ScaleType
import android.widget.Space
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.ui.view.video.VideoPageListView
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.isHttp

/**
 * Created by bo.li
 * Date: 2023/9/27
 * Desc:
 */
object FeedImageVideoUtil {
    private const val TAG = "checkcheck_feedvideo"

    // 竖图的高最高为宽度的1.3倍
    private const val RATIO_MAX_HEIGHT = 1.3F


    /**
     * 获取视频宽高
     */
    fun getVideoDisplay(width: Int?, height: Int?, contentWidth: Int, fullContentWith: Boolean = false): Pair<Int, Int> {
        return if (width == null || height == null || width <= 0 || height <= 0) {
            Pair(ViewGroup.LayoutParams.MATCH_PARENT, (contentWidth * RATIO_MAX_HEIGHT).toInt())
        } else {
            if (fullContentWith) {
                contentWidth to (height * contentWidth / width)
            } else {
                getScalePair(width, height, contentWidth)
            }
        }
    }

    fun scaleImageList(
        showSpaceStart: Boolean,
        showSpaceEnd: Boolean,
        imageView: ImageView,
        commonSize: Int,
        intervalWidth: Int,
        startWidth: Int,
        endWidth: Int,
    ) {
        val layoutParams = (imageView.layoutParams as ViewGroup.MarginLayoutParams).apply {
            height = commonSize
            width = commonSize
            marginStart = if (showSpaceStart) startWidth else intervalWidth
            marginEnd = if (showSpaceEnd) endWidth else intervalWidth
        }
        imageView.layoutParams = layoutParams
        imageView.scaleType = ScaleType.CENTER_CROP
    }

    fun scaleImageSingle(
        showSpaceStart: Boolean,
        showSpaceEnd: Boolean,
        imageView: ImageView,
        contentWidth: Int,
        originWidth: Int,
        originHeight: Int,
        startWidth: Int,
        endWidth: Int,
    ) {
        val layoutParams = (imageView.layoutParams as ViewGroup.MarginLayoutParams).apply {
            val pair = getScalePair(originWidth, originHeight, contentWidth)
            width = pair.first
            height = pair.second
            marginStart = if (!showSpaceStart) 0 else startWidth
            marginEnd = if (!showSpaceEnd) 0 else endWidth
        }
        imageView.layoutParams = layoutParams
        if (originWidth > 0 && originHeight > 0 && originHeight / originWidth >= 3) {
            // 很长的图, 从图片开始处显示
            imageView.scaleType = ScaleType.FIT_START
        } else {
            imageView.scaleType = ScaleType.CENTER_CROP
        }
    }

    private fun getScalePair(imgWidth: Int, imgHeight: Int, contentWidth: Int): Pair<Int, Int> {
        var width = 0
        var height = 0
        if (imgWidth > 0 && imgHeight > 0) {
            if (imgWidth >= imgHeight) {
                // 宽图
                val ratio = imgWidth.toFloat() / imgHeight.toFloat()
                if (ratio >= 1.25f) {
                    // 图片按16:9来显示
                    width = 288.dp
                    height = 162.dp
                } else {
                    // 图片按4:3来显示
                    width = 216.dp
                    height = 162.dp
                }
            } else {
                // 高图
                val ratio = imgHeight.toFloat() / imgWidth.toFloat()
                if (ratio >= 1.25f) {
                    // 图片按9:16来显示
                    width = 162.dp
                    height = 288.dp
                } else {
                    // 图片按3:4来显示
                    width = 162.dp
                    height = 216.dp
                }
            }
        } else {
            // 读不到图片的宽高
            // 图片按16:9来显示
            width = 288.dp
            height = 162.dp
        }
        if (contentWidth > 0 && width > contentWidth) {
            height = ((width.toFloat() * height.toFloat()) / contentWidth.toFloat()).toInt()
            width = contentWidth
        }
        return width to height
    }

    fun setVideoSize(
        spaceNotReady: Space?,
        pageListView: VideoPageListView,
        item: PostMediaResource,
        videoUrl: String,
        contentWidth: Int,
        glide: RequestManager?,
        fullContentWidth: Boolean = false
    ) {
        val defaultPair = getVideoDisplay(
            0,
            0,
            contentWidth
        )
        spaceNotReady?.layoutParams = FrameLayout.LayoutParams(defaultPair.first, defaultPair.second)
        getSize(glide, item, videoUrl) { width, height ->
            val displayPair = getVideoDisplay(
                width,
                height,
                contentWidth,
                fullContentWidth
            )
            spaceNotReady?.setSize(displayPair.first, displayPair.second)
            pageListView.setAllSize(displayPair.first, displayPair.second)
            pageListView.setCover(item.thumbnail)
        }
    }

    private fun getSize(
        glide: RequestManager?,
        item: PostMediaResource,
        videoUrl: String,
        callback: (Int, Int) -> Unit
    ) {
        if (item.resourceWidth > 0 && item.resourceHeight > 0) {
            callback(item.resourceWidth, item.resourceHeight)
        } else {
            glide?.run {
                if (videoUrl.isHttp()) {
                    asBitmap()
                } else {
                    asBitmap().skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.load(videoUrl).into(object : SimpleTarget<Bitmap?>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap?>?
                    ) {
                        callback(resource.width, resource.height)
                    }
                })
            }
        }
    }
}