package com.socialplay.gpark.function.exoplayer

import android.content.Context
import com.bumptech.glide.Glide
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.NetType
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.data.model.videofeed.VideoFeedPreloadArgs
import com.socialplay.gpark.data.model.videofeed.WrappedVideoFeedItem
import com.socialplay.gpark.data.model.videofeed.toVideoFeedItem
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.NetUtil
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference
import kotlin.random.Random

class VideoFeedPreloadInteractor(
    private val context: Context,
    private val repository: IMetaRepository,
    private val cacheInteractor: VideoPlayerCacheInteractor
) {

    private val scope = MainScope()

    private val communityPreloadArgs by lazy {
        VideoFeedPreloadArgs.fromString(PandoraToggle.controlCommunityVideoPreload)
    }
    private var communityVideoFeedPreloadJob: Job? = null
    private var communityApiResultRef = AtomicReference<VideoFeedApiResult>()

    private val partyPreloadArgs by lazy {
        VideoFeedPreloadArgs.fromString(PandoraToggle.controlPartyVideoPreload)
    }


    fun preloadCommunityVideoFeeds(onCompleted: (error:Throwable?)-> Unit) {
        Timber.d("preloadCommunityVideoFeeds start communityPreloadArgs.isEnabled:${communityPreloadArgs.isEnabled} openCommunityVideoFeedTab:${PandoraToggle.openCommunityVideoFeedTab}")
        if (!communityPreloadArgs.isEnabled || !PandoraToggle.openCommunityVideoFeedTab) {
            onCompleted(IllegalStateException("preloadCommunityVideoFeeds not enabled"))
            return
        }

        communityVideoFeedPreloadJob = scope.launch {
            while (true){
                val netType = getNetworkTypeUntilNetworkAvailable()
                Timber.d("preloadCommunityVideoFeeds networkType:${netType}")
                if (netType == NetType.M5G.desc || netType == NetType.Wifi.desc) {
                    val apiResult = runCatching {
                        repository.getRecommendVideoList(1, 20, 0, null, null).singleOrNull()
                    }.getOrNull()

                    if(apiResult != null){
                        communityApiResultRef.set(apiResult)

                        val items = apiResult.items?.map {
                            WrappedVideoFeedItem(it.toVideoFeedItem(), reqId = apiResult.reqId)
                        } ?: emptyList()

                        val cachingList = items.subList(0, minOf(communityPreloadArgs.loadCnt, items.size))

                        Timber.d("preloadCommunityVideoFeeds cachingList:${cachingList}")

                        cachingList.forEach { Glide.with(context).load(it.videoFeedItem.videoCover).preload() }

                        cacheInteractor.cacheVideoFeedList(
                            videoList = cachingList,
                            type = 2,
                            loadLength = communityPreloadArgs.loadLength,
                            cancelExists = false,
                        )
                        break
                    }
                }

                val delayMs = Random.nextLong(3_000, 10_000)
                Timber.d("apiRequest fail netType:${netType} delay:${delayMs}ms")
                // 请求失败，随机延迟，然后继续加载或者被外部取消掉Job
                delay(delayMs)
            }
        }.also {
            it.invokeOnCompletion { error ->
                onCompleted(error)
            }
        }
    }


    /**
     * 获取已经预加载的API请求结果并取消API请求任务(如果尚在请求中)
     * 此方法调用后将自动清空缓存的API请求结果，再次调用将返回null
     */
    fun getCommunityVideosAndCancelApiJob(): VideoFeedApiResult? {
        communityVideoFeedPreloadJob?.let {
            if(!it.isCompleted && !it.isCancelled){
                it.cancel()
            }
            communityVideoFeedPreloadJob = null
        }

        return communityApiResultRef.getAndSet(null)
    }

    private var pendingPartyVideoFeedApiResult: VideoFeedApiResult? = null
    private var canPreloadVideo = false

    fun preloadPartyVideoFeeds(onCompleted: (error:Throwable?)-> Unit) {
        val pending = pendingPartyVideoFeedApiResult
        if (pending == null){
            onCompleted(IllegalStateException("null pendingPartyVideoFeedApiResult"))
            return
        }
        if (canPreloadVideo){
            onCompleted(IllegalStateException("canPreloadVideo is true"))
            return
        }
        synchronized(this) {
            if (canPreloadVideo){
                onCompleted(IllegalStateException("canPreloadVideo is true"))
                return
            }
            canPreloadVideo = true
            pendingPartyVideoFeedApiResult = null
        }

        preloadPartyVideoFeedsIfNeed(pending, onCompleted)
    }

    fun preloadPartyVideoFeedsIfNeed(
        apiResult: VideoFeedApiResult,
        onCompleted: (error: Throwable?) -> Unit = {}
    ) {
        Timber.d("preloadPartyVideoFeeds partyPreloadArgs.isEnabled:${partyPreloadArgs.isEnabled} apiResult:${apiResult}")

        if (!partyPreloadArgs.isEnabled) {
            return
        }
        synchronized(this) {
            if (!canPreloadVideo) {
                pendingPartyVideoFeedApiResult = apiResult
                onCompleted(IllegalStateException("canPreloadVideo is false"))
                return
            }
        }

        scope.launch {
            val networkType = getNetworkTypeUntilNetworkAvailable()
            Timber.d("preloadPartyVideoFeeds networkType:${networkType}")

            if (networkType != NetType.Wifi.desc && networkType != NetType.M5G.desc) {
                return@launch
            }

            val items = apiResult.items?.map {
                WrappedVideoFeedItem(it.toVideoFeedItem(), reqId = apiResult.reqId)
            } ?: emptyList()

            val cachingList = items.subList(0, minOf(partyPreloadArgs.loadCnt, items.size))

            cachingList.forEach { Glide.with(context).load(it.videoFeedItem.videoCover).preload() }

            cacheInteractor.cacheVideoFeedList(
                videoList = cachingList,
                type = 1,
                loadLength = partyPreloadArgs.loadLength,
                cancelExists = false
            )
        }.invokeOnCompletion { error ->
            // for cancel callback
            onCompleted(error)
        }
    }

    suspend fun getNetworkTypeUntilNetworkAvailable(): String {
        while (!NetUtil.isNetworkAvailable()) {
            delay(1000)
        }

        return NetUtil.getNetType()
    }
}