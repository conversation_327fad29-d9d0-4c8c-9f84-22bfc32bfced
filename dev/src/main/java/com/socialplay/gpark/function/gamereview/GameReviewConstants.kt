package com.socialplay.gpark.function.gamereview

/**
 * Created by bo.li
 * Date: 2022/7/13
 * Desc:
 */
object GameReviewConstants {
    // 写评论入口点击-source-详情页write按钮
    const val SOURCE_DETAIL_WRITE = "detail_write"
    // 写评论入口点击-source-引导评论弹窗rate按钮
    const val SOURCE_DIALOG = "dialog"
    // 写评论入口点击-source-see all页Write按钮
    const val SOURCE_ALL_LIST = "all_list"

    // 别人评论展示-source-详情页
    const val SOURCE_DETAIL = "detail"
    // 别人评论展示-source-see all页Write按钮
    const val SOURCE_SHOW_ALL_LIST = "all_list"

    // 评论有/无帮助点击-type-确认
    const val TYPE_SELECT = "select"
    // 评论有/无帮助点击-type-取消
    const val TYPE_UNSELECT = "unselect"

    // 查看全部评论点击-source-详情页分数
    const val SOURCE_RATE = "rate"
    // 查看全部评论点击-source-详情页seeall按钮
    const val SOURCE_SEE_ALL = "see_all"

    // 退出发评论页面-type-编辑
    const val TYPE_EDIT = "edit"
    // 退出发评论页面-type-新写
    const val TYPE_WRITE = "write"

    // 发布评论-rate-喜欢
    const val RATE_LIKE = "like"
    // 发布评论-rate-不喜欢
    const val RATE_DISLIKE = "dislike"
}