package com.socialplay.gpark.function.analytics.handle

import android.text.TextUtils
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: 下载相关埋点
 */
object DownloadAnalytics {
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    // 记录该游戏是否已经保存了从点击开始下载到进度条回调开始的时间间隔intervalTime
    private val intervalHashMap = HashMap<String, Boolean>()

    const val FAILURE = 0
    const val SUCCESS = 1
    const val INTERRUPT = 2

    /**
     * 统计点击开始下载时间
     */
    fun recordTryDownloadStart(
        packageName: String,
        gameId: String,
        showProgress: Boolean = true
    ) {
        val forHashMap = metaKV.download.getNewDownloadMap(packageName, showProgress)
        if (forHashMap.isEmpty()) {
            forHashMap["packageName"] = packageName
        }
        if (packageName == forHashMap["packageName"]) {
            forHashMap["newStartTime"] = System.currentTimeMillis().toString()
        }
        metaKV.download.saveNewDownloadMap(packageName, showProgress, forHashMap)
        if (!metaKV.download.hasStartDownloadGame(packageName)) {
            Analytics.track(EventConstants.EVENT_DOWNLOAD_START) {
                put("packageName", packageName)
                put("gameId", gameId)
            }
        }
        metaKV.download.saveStartDownloadSet(packageName)
    }

    /**
     * 统计从点击开始下载到进度条回调开始的时间间隔
     *
     * @param packageName
     */
    fun recordProgressStart(packageName: String, showProgress: Boolean = true) {
        if (intervalHashMap[packageName] == true) {
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            val forHashMap = metaKV.download.getNewDownloadMap(packageName, showProgress)
            if (forHashMap.isNullOrEmpty()) {
                return@launch
            }
            val startRealDownload = System.currentTimeMillis()
            val intervalTime =
                startRealDownload - (forHashMap["newStartTime"]?.toLong() ?: startRealDownload)
            forHashMap["intervalTime"] = intervalTime.toString()
            intervalHashMap[packageName] = true
            metaKV.download.saveNewDownloadMap(packageName, showProgress, forHashMap)
        }
    }

    /**
     * 统计下载开始
     *
     * @param packageName
     */
    suspend fun recordDownloadStart(packageName: String, showProgress: Boolean = true) {
        withContext(Dispatchers.IO) {
            val stringStringHashMap = HashMap<String, String>()
            stringStringHashMap["packageName"] = packageName
            stringStringHashMap["time"] = System.currentTimeMillis().toString()
            metaKV.download.saveDownloadTimestamp(
                packageName,
                showProgress,
                stringStringHashMap
            )
        }
    }

    /**
     * 统计从下载到暂停下载
     *
     * @param packageName
     */
    private fun recordDownloadTime(
        packageName: String,
        showProgress: Boolean = true,
        downloadTime: Long
    ) {
        val forHashMap = metaKV.download.getNewDownloadMap(packageName, showProgress)
        if (!forHashMap.isNullOrEmpty()) {
            val totalDuration = downloadTime + (forHashMap["totalDuration"]?.toLong() ?: 0)
            forHashMap["totalDuration"] = totalDuration.toString()
        }
        metaKV.download.saveNewDownloadMap(packageName, showProgress, forHashMap)
    }

    /**
     * 统计下载结束
     *
     * @param packageName
     * @param result 0:下载失败、1:下载成功、2:中断
     */
    fun recordDownloadEnd(packageName: String, result: Int, showProgress: Boolean = true, code: Long = 0L) {
        if (TextUtils.isEmpty(packageName)) {
            return
        }
        val hashMap = metaKV.download.getDownloadTimestamp(packageName, showProgress)
        metaKV.download.saveDownloadTimestamp(packageName, showProgress, null)
        if (!hashMap.isNullOrEmpty()) {
            val packageNameTemp = hashMap["packageName"]
            if (packageName != packageNameTemp) {
                return
            }
            val time = hashMap["time"]
            val msTime =
                System.currentTimeMillis() - if (time.isNullOrEmpty()) System.currentTimeMillis() else time.toLong()
            recordDownloadTime(packageName, showProgress, msTime)
            val forHashMap = metaKV.download.getNewDownloadMap(packageName, showProgress)
            val map = HashMap<String, Any>()
            when (result) {
                FAILURE   -> {
                    map["downloadStatus"] = "failed"
                    map["downloadErrorCode"] = code
                    metaKV.download.removeStartDownloadSet(packageName)
                }
                SUCCESS   -> {
                    map["downloadStatus"] = "success"
                    metaKV.download.removeStartDownloadSet(packageName)
                }
                INTERRUPT -> {
                    map["downloadStatus"] = "interrupt"
                }
                else      -> {
                    map["downloadStatus"] = "other"
                }
            }
            val stateCount = metaKV.download.getDownloadStateCount(packageName, result) + 1
            metaKV.download.saveDownloadStateCount(packageName, result, stateCount)
            map["stateCount"] = stateCount
            map["packageName"] = packageName
            map["downloadTime"] = msTime
            map["isBackground"] = if (showProgress) "yes" else "no"
            if (!forHashMap.isNullOrEmpty() && packageName == forHashMap["packageName"]) {
                map["intervalTime"] = forHashMap["intervalTime"].toString()
                map["totalDuration"] = forHashMap["totalDuration"].toString()
            }
            Analytics.track(EventConstants.EVENT_DOWNLOAD) {
                putAll(map)
            }
        }
        if (result == SUCCESS) {
            metaKV.download.saveNewDownloadMap(packageName, showProgress, null)
        }
    }
}