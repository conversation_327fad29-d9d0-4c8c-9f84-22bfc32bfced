package com.socialplay.gpark.function.navigation

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.annotation.CallSuper
import androidx.core.content.res.use
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentManager.OnBackStackChangedListener
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.CreationExtras
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.NavigatorProvider
import androidx.navigation.NavigatorState
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.function.navigation.KeepViewNavigator.Destination
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * Navigator that navigates through [fragment transactions][FragmentTransaction]. Every destination
 * using this Navigator must set a valid Fragment class name with `android:name` or
 * [Destination.setClassName].
 *
 * The current Fragment from FragmentNavigator's perspective can be retrieved by calling
 * [FragmentManager.getPrimaryNavigationFragment] with the FragmentManager passed to this
 * FragmentNavigator.
 *
 * Note that the default implementation does Fragment transactions asynchronously, so the current
 * Fragment will not be available immediately (i.e., in callbacks to
 * [NavController.OnDestinationChangedListener]).
 *
 * FragmentNavigator respects [Log.isLoggable] for debug logging, allowing you to use `adb shell
 * setprop log.tag.FragmentNavigator VERBOSE`.
 */
@Navigator.Name("fragment")
public open class KeepViewNavigator(
    private val context: Context,
    private val fragmentManager: FragmentManager,
    private val containerId: Int
) : Navigator<Destination>() {

    // Logging for FragmentNavigator is automatically enabled along with FragmentManager logging.
    // see more at [Debug your fragments][https://developer.android.com/guide/fragments/debugging]
    private fun isLoggingEnabled(level: Int): Boolean {
        return Log.isLoggable("FragmentManager", level) || Log.isLoggable(TAG, level) || BuildConfig.DEBUG
    }

    private val savedIds = mutableSetOf<String>()

    /**
     * A list of pending operations within a Transaction expected to be executed by FragmentManager.
     * Pending ops are added at the start of a transaction, and by the time a transaction completes,
     * this list is expected to be cleared.
     *
     * In general, each entry would be added only once to this list within a single transaction
     * except in the case of singleTop transactions. Single top transactions involve two fragment
     * instances with the same entry, so we would get two onBackStackChanged callbacks on the same
     * entry.
     *
     * Each Pair represents the entry.id and whether this entry is getting popped
     */
    internal val pendingOps = mutableListOf<Pair<String, Boolean>>()

    /** Get the back stack from the [state]. */
    internal val backStack
        get() = state.backStack

    private val fragmentObserver = LifecycleEventObserver { source, event ->
        if (event == Lifecycle.Event.ON_DESTROY) {
            val fragment = source as Fragment
            val entry =
                state.transitionsInProgress.value.lastOrNull { entry -> entry.id == fragment.tag }
            Timber.tag(TAG).d("fragmentObserver ON_DESTROY source=$source event=$event entry=$entry fragment=$fragment")
            if (entry != null) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("Marking transition complete for entry $entry due to fragment $source lifecycle reaching DESTROYED")
                }
                state.markTransitionComplete(entry)
            }
        }
    }

    private val fragmentViewObserver = { entry: NavBackStackEntry ->
        LifecycleEventObserver { owner, event ->
            // Once the lifecycle reaches RESUMED, if the entry is in the back stack we can mark
            // the transition complete
            if (event == Lifecycle.Event.ON_RESUME && state.backStack.value.contains(entry)) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("fragmentObserver onResume & backStack contains entry, Marking transition complete for entry $entry due  to fragment $owner view lifecycle reaching RESUMED")
                }
                state.markTransitionComplete(entry)
            }
            // Once the lifecycle reaches DESTROYED, we can mark the transition complete
            if (event == Lifecycle.Event.ON_DESTROY) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d(
                        "fragmentObserver onDestroy & backStack contains entry, Marking transition complete for entry $entry due  to fragment $owner view lifecycle reaching DESTROYED"
                    )
                }
                state.markTransitionComplete(entry)
            }
        }
    }

    override fun onAttach(state: NavigatorState) {
        super.onAttach(state)
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("onAttach state=$state")
        }

        fragmentManager.addFragmentOnAttachListener { _, fragment ->
            val entry = state.backStack.value.lastOrNull { it.id == fragment.tag }
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("onAttach-addFragmentOnAttachListener fragment=$fragment entry=$entry FragmentManager $fragmentManager")
            }
            if (entry != null) {
                attachObservers(entry, fragment)
                // We need to ensure that if the fragment has its state saved and then that state
                // later cleared without the restoring the fragment that we also clear the state
                // of the associated entry.
                attachClearViewModel(fragment, entry, state)
            }
        }

        fragmentManager.addOnBackStackChangedListener(
            object : OnBackStackChangedListener {
                override fun onBackStackChanged() {
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("onBackStackChanged pendingOps=${pendingOps.map { "${it.first}(${it.second})" }} backStack=${state.backStack.value.map { it.id }} savedIds=${savedIds} state=${state}")
                    }
                }

                override fun onBackStackChangeStarted(fragment: Fragment, pop: Boolean) {
                    // We only care about the pop case here since in the navigate case by the time
                    // we get here the fragment will have already been moved to STARTED.
                    // In the case of a pop, we move the entries to STARTED
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("onBackStackChangeStarted fragment=$fragment pop=$pop pendingOps=${pendingOps.map { "${it.first}(${it.second})" }} backStack=${state.backStack.value.map { it.id }} savedIds=${savedIds} state=${state}")
                    }
                    if (pop) {
                        val entry = state.backStack.value.lastOrNull { it.id == fragment.tag }
                        if (isLoggingEnabled(Log.VERBOSE)) {
                            Timber.tag(TAG).d("OnBackStackChangedStarted for fragment  $fragment associated with entry $entry")
                        }
                        entry?.let { state.prepareForTransition(it) }
                    }
                }

                override fun onBackStackChangeCommitted(fragment: Fragment, pop: Boolean) {
                    val entry =
                        (state.backStack.value + state.transitionsInProgress.value).lastOrNull {
                            it.id == fragment.tag
                        }
                    // 改进：更准确的系统返回键检测
                    val isSystemBack = pop && pendingOps.isEmpty() && fragment.isRemoving
                    val op = pendingOps.firstOrNull { it.first == fragment.tag }
                    op?.let { pendingOps.remove(it) }


                    val popOp = op?.second == true

                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("onBackStackChangeCommitted fragment=$fragment pop=$pop entry=$entry isSystemBack=$isSystemBack op=$op popOp=$popOp pendingOps=${pendingOps.map { "${it.first}(${it.second})" }} backStack=${state.backStack.value.map { it.id }} savedIds=${savedIds} state=${state}")
                    }
                    // 改进：处理entry为null的情况
                    if (!pop && !popOp) {
                        if (entry == null) {
                            // 孤儿Fragment处理
                            Timber.tag(TAG).w("Orphan fragment detected: ${fragment.javaClass.simpleName}(${fragment.tag})")
                            try {
                                if (fragment.view != null && fragment.isAdded) {
                                    Timber.tag(TAG).w("Orphan fragment with active view: ${fragment.javaClass.simpleName}")
                                    // 添加动画避免界面闪烁
                                    fragmentManager.beginTransaction()
                                        .setCustomAnimations(android.R.anim.fade_in, android.R.anim.fade_out)
                                        .remove(fragment)
                                        .commitAllowingStateLoss()
                                } else {
                                    // 直接移除无View的Fragment
                                    fragmentManager.beginTransaction()
                                        .remove(fragment)
                                        .commitAllowingStateLoss()
                                }
                            } catch (e: Exception) {
                                Timber.tag(TAG).e(e, "Failed to remove orphan fragment: ${fragment.tag}")
                            }
                            return
                        }
                        checkNotNull(entry) {
                            "Fragment ${fragment.javaClass.simpleName}(${fragment.tag}) " +
                                    "not managed by KeepViewNavigator. " +
                                    "BackStack: ${state.backStack.value.map { it.id }}, " +
                                    "PendingOps: ${pendingOps.map { it.first }}"
                        }
                    }

                    if (entry != null) {
                        // In case we get a fragment that was never attached to the fragment
                        // manager,
                        // we need to make sure we still return the entries to their proper final
                        // state.
                        attachClearViewModel(fragment, entry, state)
                        // This is the case of system back where we will need to make the call to
                        // popBackStack. Otherwise, popBackStack was called directly and we avoid
                        // popping again.
                        if (isSystemBack) {
                            if (isLoggingEnabled(Log.VERBOSE)) {
                                Timber.tag(TAG).d("OnBackStackChangedCommitted for fragment $fragment  popping associated entry $entry via ${if (isSystemBack) "system back" else "setPopUpTo"}")
                            }
                            state.popWithTransition(entry, false)
                        }
                    }
                }
            }
        )
    }

    private fun attachObservers(entry: NavBackStackEntry, fragment: Fragment) {
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("attachObservers entry=$entry fragment=$fragment")
        }
        fragment.viewLifecycleOwnerLiveData.observe(fragment) { owner ->

            // attach observer unless it was already popped at this point
            // we get onBackStackStackChangedCommitted callback for an executed navigate where we
            // remove incoming fragment from pendingOps before ATTACH so the listener will still
            // be added
            val isPending = pendingOps.any { it.first == fragment.tag }
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("attachObservers viewLifecycleOwnerLiveData isPending=$isPending owner=$owner entry=$entry fragment=$fragment")
            }
            if (owner != null && !isPending) {
                val viewLifecycle = fragment.viewLifecycleOwner.lifecycle
                // We only need to add observers while the viewLifecycle has not reached a final
                // state
                if (viewLifecycle.currentState.isAtLeast(Lifecycle.State.CREATED)) {
                    viewLifecycle.addObserver(fragmentViewObserver(entry))
                }
            }
        }
        fragment.lifecycle.addObserver(fragmentObserver)
    }

    internal fun attachClearViewModel(
        fragment: Fragment,
        entry: NavBackStackEntry,
        state: NavigatorState
    ) {
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("attachClearViewModel fragment=$fragment entry=$entry state=$state")
        }
        val viewModel =
            ViewModelProvider(
                fragment.viewModelStore,
                viewModelFactory { initializer { ClearEntryStateViewModel() } },
                CreationExtras.Empty
            )[ClearEntryStateViewModel::class.java]
        viewModel.completeTransition = WeakReference {
            // 增加Fragment状态校验
            if (!fragment.isAdded || fragment.isDetached) {
                Timber.d("attachClearViewModel Fragment already detached, skip transition complete")
                return@WeakReference
            }
            entry.let {
                state.transitionsInProgress.value.forEach { entry ->
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("attachClearViewModel transitionsInProgress, Marking transition complete for entry $entry due to fragment $fragment viewmodel being cleared")
                    }
                    state.markTransitionComplete(entry)
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     *
     * This method must call [FragmentTransaction.setPrimaryNavigationFragment] if the pop succeeded
     * so that the newly visible Fragment can be retrieved with
     * [FragmentManager.getPrimaryNavigationFragment].
     *
     * Note that the default implementation pops the Fragment asynchronously, so the newly visible
     * Fragment from the back stack is not instantly available after this call completes.
     */
    override fun popBackStack(popUpTo: NavBackStackEntry, savedState: Boolean) {
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).d("popBackStack - Ignoring popBackStack() call: FragmentManager has already saved its state")
            return
        }
        // 增加栈深度校验
        if (backStack.value.indexOf(popUpTo) < 0) {
            Timber.tag(TAG).e("popBackStack Invalid popUpTo entry ${popUpTo.id} not found in back stack")
            return
        }
        val beforePopList = state.backStack.value
        // Get the set of entries that are going to be popped
        val popUpToIndex = beforePopList.indexOf(popUpTo)
        val poppedList = beforePopList.subList(popUpToIndex, beforePopList.size)
        val initialEntry = beforePopList.first()

        // 改进：更准确的pendingOps管理
        // add pending ops here before any animation (if present) or FragmentManager work starts
        val incomingEntry = beforePopList.elementAtOrNull(popUpToIndex - 1)
        if (incomingEntry != null) {
            addPendingOps(incomingEntry.id)
            Timber.tag(TAG).d("popBackStack - Added pending op for incoming entry: ${incomingEntry.id}")
        }
        if(isLoggingEnabled(Log.VERBOSE)){
            Timber.tag(TAG).d("popBackStack - popUpTo=$popUpTo savedState=$savedState, beforePopList=${beforePopList.map { it.id }}, popUpToIndex=$popUpToIndex, poppedList=${poppedList.map { it.id }}, incomingEntry=${incomingEntry?.id}")
        }

        // 添加被pop的entries到pendingOps
        poppedList
            .filter { entry ->
                // normally we don't add initialEntry to pending ops because the adding/popping
                // of an isolated fragment does not trigger onBackStackCommitted. But if initial
                // entry was already added to pendingOps, it was likely an incomingEntry that now
                // needs to be popped, so we need to overwrite isPop to true here.
                pendingOps.asSequence().map { it.first }.contains(entry.id) ||
                        entry.id != initialEntry.id
            }
            .forEach { entry ->
                addPendingOps(entry.id, isPop = true)
                Timber.tag(TAG).d("popBackStack Added pending op for popped entry: $entry")
            }

        if (savedState) {
            // Now go through the list in reversed order (i.e., started from the most added)
            // and save the back stack state of each.
            for (entry in poppedList.reversed()) {
                if (entry == initialEntry) {
                    Timber.tag(TAG).i("popBackStack FragmentManager cannot save the state of the initial destination $entry")
                } else {
                    try {
                        fragmentManager.saveBackStack(entry.id)
                        savedIds += entry.id
                        Timber.tag(TAG).d("popBackStack Saved state for entry: ${entry.id}")
                    } catch (e: Exception) {
                        Timber.tag(TAG).e(e, "popBackStack Failed to save state for entry: ${entry.id}")
                    }
                }
            }
        } else {
            // 改进：添加更详细的日志和错误处理
            Timber.tag(TAG).d("popBackStack Popping back stack to: ${popUpTo.id}, inclusive: true")
            Timber.tag(TAG).d("popBackStack Entries to be popped: ${poppedList.map { it.id }}")

            try {
                fragmentManager.popBackStack(popUpTo.id, FragmentManager.POP_BACK_STACK_INCLUSIVE)
                Timber.tag(TAG).d("popBackStack Successfully popped back stack to: ${popUpTo.id}")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "popBackStack Failed to pop back stack to: ${popUpTo.id}")
                // 尝试恢复状态
                pendingOps.clear()
                return
            }
        }

        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("popBackStack popWithTransition via popBackStack() on entry  $popUpTo with savedState $savedState")
        }

        state.popWithTransition(popUpTo, savedState)
    }

    public override fun createDestination(): Destination {
        return Destination(this)
    }

    /**
     * Instantiates the Fragment via the FragmentManager's [androidx.fragment.app.FragmentFactory].
     *
     * Note that this method is **not** responsible for calling [Fragment.setArguments] on the
     * returned Fragment instance.
     *
     * @param context Context providing the correct [ClassLoader]
     * @param fragmentManager FragmentManager the Fragment will be added to
     * @param className The Fragment to instantiate
     * @param args The Fragment's arguments, if any
     * @return A new fragment instance.
     */
    @Suppress("DeprecatedCallableAddReplaceWith")
    @Deprecated(
        """Set a custom {@link androidx.fragment.app.FragmentFactory} via
      {@link FragmentManager#setFragmentFactory(FragmentFactory)} to control
      instantiation of Fragments."""
    )
    public open fun instantiateFragment(
        context: Context,
        fragmentManager: FragmentManager,
        className: String,
        args: Bundle?
    ): Fragment {
        return fragmentManager.fragmentFactory.instantiate(context.classLoader, className)
    }

    /**
     * {@inheritDoc}
     *
     * This method should always call [FragmentTransaction.setPrimaryNavigationFragment] so that the
     * Fragment associated with the new destination can be retrieved with
     * [FragmentManager.getPrimaryNavigationFragment].
     *
     * Note that the default implementation commits the new Fragment asynchronously, so the new
     * Fragment is not instantly available after this call completes.
     *
     * This call will be ignored if the FragmentManager state has already been saved.
     */
    override fun navigate(
        entries: List<NavBackStackEntry>,
        navOptions: NavOptions?,
        navigatorExtras: Navigator.Extras?
    ) {
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).d("Ignoring navigate() call: FragmentManager has already saved its state")
            return
        }
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("navigate entries=$entries navOptions=$navOptions navigatorExtras=$navigatorExtras")
        }
        for (entry in entries) {
            navigate(entry, navOptions, navigatorExtras)
        }
    }

    private fun navigate(
        entry: NavBackStackEntry,
        navOptions: NavOptions?,
        navigatorExtras: Navigator.Extras?
    ) {
        val initialNavigation = state.backStack.value.isEmpty()

        // 处理launchSingleTop逻辑
        if (navOptions?.shouldLaunchSingleTop() == true) {
            handleLaunchSingleTop(entry, navOptions)
            return
        }

        // 处理popUpTo逻辑
        if (navOptions?.popUpToId != null || !navOptions?.popUpToRoute.isNullOrBlank()) {
            handlePopUpTo(navOptions, entry)
        }

        val restoreState =
            (navOptions != null &&
                    !initialNavigation &&
                    navOptions.shouldRestoreState() &&
                    savedIds.contains(entry.id))
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("navigate entry=$entry navOptions=$navOptions navigatorExtras=$navigatorExtras initialNavigation=$initialNavigation restoreState=$restoreState backStack=${state.backStack.value.map { it.destination }}")
        }
        if (restoreState) {
            // 使用新的状态恢复方法
            if (handleStateRestoration(entry)) {
                state.pushWithTransition(entry)
                return
            } else {
                // 状态恢复失败，继续执行正常的navigate逻辑
                Timber.tag(TAG).w("navigate State restoration failed for entry: ${entry.id}, proceeding with normal navigation")
            }
        }
        val ft = createFragmentTransaction(entry, navOptions)

        if (!initialNavigation) {
            val outgoingEntry = state.backStack.value.lastOrNull()
            // if outgoing entry is initial entry, FragmentManager still triggers onBackStackChange
            // callback for it, so we don't filter out initial entry here
            if (outgoingEntry != null) {
                addPendingOps(outgoingEntry.id)
            }
            // add pending ops here before any animation (if present) starts
            addPendingOps(entry.id)
            Timber.tag(TAG).d("navigate Added pending op for incoming entry: ${entry.id}")
            ft.addToBackStack(entry.id)
        }

        if (navigatorExtras is Extras) {
            for ((key, value) in navigatorExtras.sharedElements) {
                ft.addSharedElement(key, value)
            }
        }
        ft.commit()
        // The commit succeeded, update our view of the world
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("navigate pushWithTransition via navigate() on entry $entry")
        }
        state.pushWithTransition(entry)
    }

    /**
     * 处理popUpTo逻辑，支持通过ID和Route进行popUpTo操作
     */
    private fun handlePopUpTo(navOptions: NavOptions, entry: NavBackStackEntry) {
        val popUpToId = navOptions.popUpToId
        val popUpToRoute = navOptions.popUpToRoute
        val inclusive = navOptions.isPopUpToInclusive()
        val saveState = navOptions.shouldPopUpToSaveState()

        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("handlePopUpTo popUpToId=$popUpToId popUpToRoute=$popUpToRoute inclusive=$inclusive saveState=$saveState")
        }

        // 检查是否需要执行popUpTo
        if (popUpToId == -1 && popUpToRoute.isNullOrBlank()) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("handlePopUpTo No popUpTo specified, skipping")
            }
            return
        }

        // 确定popUpTo的目标entry
        val targetEntry = findPopUpToTarget(popUpToId, popUpToRoute)

        if (targetEntry == null) {
            Timber.tag(TAG).w("handlePopUpTo Target entry not found: popUpToId=$popUpToId popUpToRoute=$popUpToRoute")
            return
        }

        handlePopUpToInternal(targetEntry, inclusive, saveState)
    }

    /**
     * 查找popUpTo的目标entry
     */
    private fun findPopUpToTarget(popUpToId: Int?, popUpToRoute: String?): NavBackStackEntry? {
        val currentBackStack = state.backStack.value

        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("findPopUpToTarget Current backStack: ${currentBackStack.map { "${it.id}(${it.destination.id})" }}")
        }

        return when {
            !popUpToRoute.isNullOrBlank() -> {
                // 通过route查找目标entry
                currentBackStack.find { it.destination.route == popUpToRoute }
            }
            popUpToId != null && popUpToId != -1 -> {
                // 通过ID查找目标entry
                val targetByDestinationId = currentBackStack.find { it.destination.id == popUpToId }
                if (targetByDestinationId != null) {
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("findPopUpToTarget Found target by destination ID: $popUpToId -> ${targetByDestinationId.id}")
                    }
                    return targetByDestinationId
                }

                // 如果通过destination.id找不到，尝试通过entry.id查找（可能是动态生成的ID）
                val targetByEntryId = currentBackStack.find { it.id == popUpToId.toString() }
                if (targetByEntryId != null) {
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("findPopUpToTarget Found target by entry ID string: $popUpToId -> ${targetByEntryId.id}")
                    }
                    return targetByEntryId
                }

                // 如果还是找不到，尝试查找最后一个匹配的destination
                val lastMatchingEntry = currentBackStack.findLast { it.destination.id == popUpToId }
                if (lastMatchingEntry != null) {
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("findPopUpToTarget Found last matching destination: $popUpToId -> ${lastMatchingEntry.id}")
                    }
                    return lastMatchingEntry
                }

                // 如果还是找不到，尝试智能匹配
                val smartTarget = findSmartPopUpToTarget(popUpToId, currentBackStack)
                if (smartTarget != null) {
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("findPopUpToTarget Found smart target: $popUpToId -> ${smartTarget.id}")
                    }
                    return smartTarget
                }

                null
            }
            else -> null
        }
    }

    /**
     * 智能查找popUpTo目标
     * 当找不到精确匹配时，尝试查找最接近的目标
     */
    private fun findSmartPopUpToTarget(popUpToId: Int, currentBackStack: List<NavBackStackEntry>): NavBackStackEntry? {
        // 常见的导航模式：从聊天列表到群聊，popUpTo通常是聊天列表
        // 检查是否是聊天相关的导航
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("findSmartPopUpToTarget Attempting smart match for popUpToId: $popUpToId")
        }

        // 如果当前backStack中有聊天相关的Fragment，尝试找到最合适的popUpTo目标
        val chatRelatedEntries = currentBackStack.filter { entry ->
            val destId = entry.destination.id
            // 检查是否是聊天相关的destination
            destId == R.id.chat_tab ||
            destId == R.id.group_chat_fragment ||
            destId == R.id.contact_list_fragment ||
            entry.destination.route?.contains("chat") == true ||
            entry.destination.route?.contains("conversation") == true
        }

        if (chatRelatedEntries.isNotEmpty()) {
            // 找到最后一个聊天相关的entry作为popUpTo目标
            val smartTarget = chatRelatedEntries.last()
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("findSmartPopUpToTarget Found chat-related target: ${smartTarget.id} (${smartTarget.destination.id})")
            }
            return smartTarget
        }

        // 如果没有找到聊天相关的，尝试找到倒数第二个entry（通常是合理的popUpTo目标）
        if (currentBackStack.size > 1) {
            val fallbackTarget = currentBackStack[currentBackStack.size - 2]
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("findSmartPopUpToTarget Using fallback target: ${fallbackTarget.id} (${fallbackTarget.destination.id})")
            }
            return fallbackTarget
        }

        return null
    }

    /**
     * 内部处理popUpTo逻辑
     */
    private fun handlePopUpToInternal(targetEntry: NavBackStackEntry, inclusive: Boolean, saveState: Boolean) {
        // 检查是否需要popUpTo
        val currentBackStack = state.backStack.value
        val targetIndex = currentBackStack.indexOf(targetEntry)
        val currentIndex = currentBackStack.size - 1

        if (targetIndex < 0 || targetIndex >= currentIndex) {
            Timber.tag(TAG).d("handlePopUpToInternal No need to pop: targetIndex=$targetIndex currentIndex=$currentIndex")
            return
        }

        // 计算需要pop的entries
        val entriesToPop = if (inclusive) {
            currentBackStack.subList(targetIndex, currentBackStack.size)
        } else {
            currentBackStack.subList(targetIndex + 1, currentBackStack.size)
        }

        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("handlePopUpToInternal Entries to pop: ${entriesToPop.map { it.id }} currentBackStack=${currentBackStack.map { it.id }}")
        }

        // 保存状态（如果需要）
        if (saveState) {
            for (entryToPop in entriesToPop.reversed()) {
                if (entryToPop != currentBackStack.first()) { // 不保存初始destination的状态
                    try {
                        fragmentManager.saveBackStack(entryToPop.id)
                        savedIds += entryToPop.id
                        Timber.tag(TAG).d("handlePopUpToInternal Saved state for entry: ${entryToPop.id}")
                    } catch (e: Exception) {
                        Timber.tag(TAG).e(e, "handlePopUpToInternal Failed to save state for entry: ${entryToPop.id}")
                    }
                }
            }
        }

        // 添加pending ops
        entriesToPop.forEach { entryToPop ->
            addPendingOps(entryToPop.id, isPop = true)
        }

        // 执行popBackStack
        val popUpToEntryId = if (inclusive) targetEntry.id else entriesToPop.first().id
        try {
            fragmentManager.popBackStack(popUpToEntryId, FragmentManager.POP_BACK_STACK_INCLUSIVE)
            Timber.tag(TAG).d("handlePopUpToInternal Successfully popped back stack to: $popUpToEntryId")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "handlePopUpToInternal Failed to pop back stack to: $popUpToEntryId")
        }
    }

    /**
     * 处理launchSingleTop逻辑
     */
    private fun handleLaunchSingleTop(entry: NavBackStackEntry, navOptions: NavOptions) {
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("handleLaunchSingleTop entry=$entry")
        }

        // 检查是否已经存在相同的destination
        val existingEntry = state.backStack.value.find { it.destination.id == entry.destination.id }

        if (existingEntry != null) {
            // 如果存在相同的destination，执行singleTop逻辑
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("handleLaunchSingleTop Found existing entry: ${existingEntry.id}")
            }

            // 创建FragmentTransaction
            val ft = createFragmentTransaction(entry, navOptions)
            val backstack = state.backStack.value

            if (backstack.size > 1) {
                // 如果目标Fragment在back stack中，需要先pop到它，然后替换
                val incomingEntry = backstack.elementAtOrNull(backstack.lastIndex - 1)
                if (incomingEntry != null) {
                    addPendingOps(incomingEntry.id)
                }
                addPendingOps(existingEntry.id, isPop = true)

                // 处理popUpTo（如果设置了）
                if (navOptions.popUpToId != null || !navOptions.popUpToRoute.isNullOrBlank()) {
                    handlePopUpTo(navOptions, entry)
                } else {
                    // 默认pop到existingEntry
                    fragmentManager.popBackStack(existingEntry.id, FragmentManager.POP_BACK_STACK_INCLUSIVE)
                }

                addPendingOps(entry.id, deduplicate = false)
                ft.addToBackStack(entry.id)
            }

            ft.commit()
            state.onLaunchSingleTop(entry)
        } else {
            // 如果不存在相同的destination，执行普通的navigate逻辑
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("handleLaunchSingleTop No existing entry found, proceeding with normal navigation")
            }
            // 继续执行原有的navigate逻辑
        }
    }

    /**
     * {@inheritDoc}
     *
     * This method should always call [FragmentTransaction.setPrimaryNavigationFragment] so that the
     * Fragment associated with the new destination can be retrieved with
     * [FragmentManager.getPrimaryNavigationFragment].
     *
     * Note that the default implementation commits the new Fragment asynchronously, so the new
     * Fragment is not instantly available after this call completes.
     *
     * This call will be ignored if the FragmentManager state has already been saved.
     */
    override fun onLaunchSingleTop(backStackEntry: NavBackStackEntry) {
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).i("Ignoring onLaunchSingleTop() call: FragmentManager has already saved its state")
            return
        }

        // 使用新的handleLaunchSingleTop方法处理
        handleLaunchSingleTop(backStackEntry, NavOptions.Builder().build())
    }

    private fun createFragmentTransaction(
        entry: NavBackStackEntry,
        navOptions: NavOptions?
    ): FragmentTransaction {
        // 增加fragment状态校验
        if (fragmentManager.isDestroyed) {
            Timber.tag(TAG).w("createFragmentTransaction FragmentManager already destroyed, skip transaction")
            return fragmentManager.beginTransaction().apply { commitAllowingStateLoss() }
        }
        val destination = entry.destination as Destination
        val args = entry.arguments
        var className = destination.className
        if (className[0] == '.') {
            className = context.packageName + className
        }

        val ft = fragmentManager.beginTransaction()

        // 处理动画
        var enterAnim = navOptions?.enterAnim ?: -1
        var exitAnim = navOptions?.exitAnim ?: -1
        var popEnterAnim = navOptions?.popEnterAnim ?: -1
        var popExitAnim = navOptions?.popExitAnim ?: -1
        if (enterAnim != -1 || exitAnim != -1 || popEnterAnim != -1 || popExitAnim != -1) {
            enterAnim = if (enterAnim != -1) enterAnim else 0
            exitAnim = if (exitAnim != -1) exitAnim else 0
            popEnterAnim = if (popEnterAnim != -1) popEnterAnim else 0
            popExitAnim = if (popExitAnim != -1) popExitAnim else 0
            ft.setCustomAnimations(enterAnim, exitAnim, popEnterAnim, popExitAnim)
        }

        // 判断是否需要重启（完全替换所有Fragment）
        val needRestart = when {
            destination.id == R.id.guideLogin -> true
            navOptions?.popUpToId == R.id.main -> true
            navOptions?.popUpToRoute == "main" -> true
            // 可以添加其他需要重启的场景
            else -> false
        }

        var cachedFragment: Fragment? = null
        if (!needRestart) {
            // 保留Fragment状态的逻辑
            fragmentManager.fragments.forEach { fragment ->
                if (fragment.tag == entry.id) {
                    cachedFragment = fragment
                } else if (fragment.arguments?.getBoolean(ARG_KEEP) != true) {
                    // 非保留的Fragment直接移除
                    ft.remove(fragment)
                } else {
                    // 保留的Fragment隐藏并设置生命周期
                    ft.hide(fragment)
                    ft.setMaxLifecycle(fragment, Lifecycle.State.STARTED)
                }
            }

            val frag: Fragment
            if (cachedFragment != null) {
                // 使用缓存的Fragment
                frag = cachedFragment!!
                ft.show(frag)
                ft.setMaxLifecycle(frag, Lifecycle.State.RESUMED)
                Timber.tag(TAG).d("createFragmentTransaction Using cached fragment for ${entry.id}")
            } else {
                // 创建新的Fragment
                frag = fragmentManager.fragmentFactory.instantiate(context.classLoader, className)
                frag.arguments = args
                ft.add(containerId, frag, entry.id)
                Timber.tag(TAG).d("createFragmentTransaction Created new fragment for ${entry.id}")
            }
            ft.setPrimaryNavigationFragment(frag)
            ft.setReorderingAllowed(true)
        } else {
            // 重启逻辑：隐藏所有保留的Fragment，替换为新的Fragment
            fragmentManager.fragments.forEach { fragment ->
                if (fragment.arguments?.getBoolean(ARG_KEEP) == true) {
                    ft.hide(fragment)
                    ft.setMaxLifecycle(fragment, Lifecycle.State.CREATED)
                }
            }
            val frag = fragmentManager.fragmentFactory.instantiate(context.classLoader, className)
            frag.arguments = args
            ft.replace(containerId, frag, entry.id)
            ft.setPrimaryNavigationFragment(frag)
            ft.setReorderingAllowed(true)
            Timber.tag(TAG).d("createFragmentTransaction Restart mode for ${entry.id}")
        }

        Timber.tag(TAG).d("Creating transaction for ${entry.id} needRestart=$needRestart cachedFragment=$cachedFragment entry=$entry navOptions=$navOptions")
        return ft
    }

    public override fun onSaveState(): Bundle? {
        Timber.tag(TAG).d("onSaveState savedIds=$savedIds")
        if (savedIds.isEmpty()) {
            return null
        }
        return bundleOf(KEY_SAVED_IDS to ArrayList(savedIds))
    }

    public override fun onRestoreState(savedState: Bundle) {
        Timber.tag(TAG).d("onRestoreState savedIds=$savedIds")
        val savedIds = savedState.getStringArrayList(KEY_SAVED_IDS)
        if (savedIds != null) {
            this.savedIds.clear()
            this.savedIds += savedIds
            // 清理无效的保存状态
            cleanupInvalidSavedStates()
        }
    }

    /**
     * 清理无效的保存状态
     */
    private fun cleanupInvalidSavedStates() {
        val invalidIds = mutableListOf<String>()
        savedIds.forEach { id ->
            try {
                // 尝试恢复状态来验证是否有效
                fragmentManager.restoreBackStack(id)
                // 如果成功，立即保存回来
                fragmentManager.saveBackStack(id)
            } catch (e: Exception) {
                Timber.tag(TAG).w("cleanupInvalidSavedStates Invalid saved state found: $id")
                invalidIds.add(id)
            }
        }

        if (invalidIds.isNotEmpty()) {
            savedIds.removeAll(invalidIds)
            Timber.tag(TAG).d("cleanupInvalidSavedStates Removed ${invalidIds.size} invalid saved states: $invalidIds")
        }
    }

    /**
     * NavDestination specific to [FragmentNavigator]
     *
     * Construct a new fragment destination. This destination is not valid until you set the
     * Fragment via [setClassName].
     *
     * @param fragmentNavigator The [FragmentNavigator] which this destination will be associated
     *   with. Generally retrieved via a [NavController]'s [NavigatorProvider.getNavigator] method.
     */
    @NavDestination.ClassType(Fragment::class)
    public open class Destination
    public constructor(fragmentNavigator: Navigator<out Destination>) :
        NavDestination(fragmentNavigator) {

        /**
         * Construct a new fragment destination. This destination is not valid until you set the
         * Fragment via [setClassName].
         *
         * @param navigatorProvider The [NavController] which this destination will be associated
         *   with.
         */
        public constructor(
            navigatorProvider: NavigatorProvider
        ) : this(navigatorProvider.getNavigator(KeepViewNavigator::class.java))

        @CallSuper
        public override fun onInflate(context: Context, attrs: AttributeSet) {
//            Timber.tag(TAG).d("onInflate context=$context attrs=$attrs")
            super.onInflate(context, attrs)
            context.resources.obtainAttributes(
                attrs,
                androidx.navigation.fragment.R.styleable.FragmentNavigator
            ).use { array ->
                val className =
                    array.getString(androidx.navigation.fragment.R.styleable.FragmentNavigator_android_name)
                if (className != null) setClassName(className)
            }
        }

        /**
         * Set the Fragment class name associated with this destination
         *
         * @param className The class name of the Fragment to show when you navigate to this
         *   destination
         * @return this [Destination]
         */
        public fun setClassName(className: String): Destination {
//            Timber.tag(TAG).d("setClassName className=$className")
            _className = className
            return this
        }

        private var _className: String? = null

        /**
         * The Fragment's class name associated with this destination
         *
         * @throws IllegalStateException when no Fragment class was set.
         */
        public val className: String
            get() {
                checkNotNull(_className) { "Fragment class was not set" }
                return _className as String
            }

        public override fun toString(): String {
            val sb = StringBuilder()
            sb.append(super.toString())
            sb.append(" class=")
            if (_className == null) {
                sb.append("null")
            } else {
                sb.append(_className)
            }
            return sb.toString()
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other == null || other !is Destination) return false
            return super.equals(other) && _className == other._className
        }

        override fun hashCode(): Int {
            var result = super.hashCode()
            result = 31 * result + _className.hashCode()
            return result
        }
    }

    /** Extras that can be passed to FragmentNavigator to enable Fragment specific behavior */
    public class Extras internal constructor(sharedElements: Map<View, String>) : Navigator.Extras {
        private val _sharedElements = LinkedHashMap<View, String>()

        /**
         * The map of shared elements associated with these Extras. The returned map is an
         * [unmodifiable][Map] copy of the underlying map and should be treated as immutable.
         */
        public val sharedElements: Map<View, String>
            get() = _sharedElements.toMap()

        /**
         * Builder for constructing new [Extras] instances. The resulting instances are immutable.
         */
        public class Builder {
            private val _sharedElements = LinkedHashMap<View, String>()

            /**
             * Adds multiple shared elements for mapping Views in the current Fragment to
             * transitionNames in the Fragment being navigated to.
             *
             * @param sharedElements Shared element pairs to add
             * @return this [Builder]
             */
            public fun addSharedElements(sharedElements: Map<View, String>): Builder {
                for ((view, name) in sharedElements) {
                    addSharedElement(view, name)
                }
                return this
            }

            /**
             * Maps the given View in the current Fragment to the given transition name in the
             * Fragment being navigated to.
             *
             * @param sharedElement A View in the current Fragment to match with a View in the
             *   Fragment being navigated to.
             * @param name The transitionName of the View in the Fragment being navigated to that
             *   should be matched to the shared element.
             * @return this [Builder]
             * @see FragmentTransaction.addSharedElement
             */
            public fun addSharedElement(sharedElement: View, name: String): Builder {
                _sharedElements[sharedElement] = name
                return this
            }

            /**
             * Constructs the final [Extras] instance.
             *
             * @return An immutable [Extras] instance.
             */
            public fun build(): Extras {
                return Extras(_sharedElements)
            }
        }

        init {
            _sharedElements.putAll(sharedElements)
        }
    }

    companion object {
        private const val TAG = "FragmentNavigator"
        private const val KEY_SAVED_IDS = "androidx-nav-fragment:navigator:savedIds"

        const val ARG_KEEP = "_arg_cyc_keep"
    }

    internal class ClearEntryStateViewModel : ViewModel() {
        lateinit var completeTransition: WeakReference<() -> Unit>

        override fun onCleared() {
            super.onCleared()
            completeTransition.get()?.invoke()
        }
    }

    /**
     * 处理状态恢复逻辑
     */
    private fun handleStateRestoration(entry: NavBackStackEntry): Boolean {
        if (!savedIds.contains(entry.id)) {
            return false
        }

        try {
            fragmentManager.restoreBackStack(entry.id)
            savedIds.remove(entry.id)
            Timber.tag(TAG).d("handleStateRestoration Successfully restored state for entry: ${entry.id}")
            return true
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "handleStateRestoration Failed to restore state for entry: ${entry.id}")
            savedIds.remove(entry.id) // 移除无效的保存状态
            return false
        }
    }

    /**
     * 验证entry是否有效
     */
    private fun isValidEntry(id: String): Boolean {
        return state.backStack.value.any { it.id == id } ||
                state.transitionsInProgress.value.any { it.id == id }
    }

    /**
     * 验证entry是否可以添加到pendingOps
     * 新创建的entry在navigate过程中可能还没有被添加到backStack中
     */
    private fun canAddToPendingOps(id: String): Boolean {
        // 检查是否在backStack或transitionsInProgress中
        val isValidEntry = state.backStack.value.any { it.id == id } ||
                state.transitionsInProgress.value.any { it.id == id }

        // 如果是新创建的entry，允许添加（在navigate过程中）
        // 或者如果已经在pendingOps中，说明是正在处理的entry
        val isNewEntry = !isValidEntry && (
            pendingOps.any { it.first == id } ||
            state.transitionsInProgress.value.any { it.id == id }
        )

        return isValidEntry || isNewEntry
    }

    /**
     * In general, each entry would only get one callback within a transaction except for single top
     * transactions, where we would get two callbacks for the same entry.
     */
    private fun addPendingOps(id: String, isPop: Boolean = false, deduplicate: Boolean = true) {
        // 改进entry验证逻辑
        if (!canAddToPendingOps(id)) {
            // 在navigate过程中，允许添加新entry（即使还没有在backStack中）
            val isNavigating = state.transitionsInProgress.value.isNotEmpty() ||
                              pendingOps.any { !it.second } // 有非pop的pending op

            if (!isNavigating) {
                Timber.tag(TAG).w("addPendingOps Attempting to add invalid pending op: $id")
                return
            } else {
                Timber.tag(TAG).d("addPendingOps Allowing new entry during navigation: $id")
            }
        }

        if (deduplicate) {
            val beforeSize = pendingOps.size
            pendingOps.removeAll { it.first == id }
            val afterSize = pendingOps.size
            val removedCount = beforeSize - afterSize
            if (removedCount > 0) {
                Timber.tag(TAG).d("addPendingOps Removed $removedCount duplicate pending ops for id: $id")
            }
        }
        // 增加生命周期状态校验
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).w("addPendingOps FragmentManager state already saved, skip adding pending op: $id")
            pendingOps.clear()
            return
        }
        pendingOps.add(id to isPop)
        Timber.tag(TAG).d("addPendingOps Added pending op: id=$id, isPop=$isPop, totalPendingOps=${pendingOps.size}")

        // 安全检查：如果pendingOps过多，可能是状态不同步
        if (pendingOps.size > 10) {
            Timber.w("addPendingOps Too many pending ops (${pendingOps.size}), possible state desync: ${pendingOps.map { "${it.first}(${it.second})" }}")
        }
    }
}
