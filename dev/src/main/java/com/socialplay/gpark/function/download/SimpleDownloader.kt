package com.socialplay.gpark.function.download

import com.meta.p4n.a3.p4n_c2e_s4w.d8r.DownloaderFactory
import com.meta.p4n.a3.p4n_c2e_s4w.d8r.interfaces.callbacks.IDownloadCacheCleanup
import com.socialplay.gpark.util.Md5Util.file2Md5Low
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * create by: bin on 2021/6/3
 */
object SimpleDownloader {
    private val queue = DownloaderFactory.newQueue()

    fun isDownloading(file: File): Boolean {
        val name = file.name
        return queue.isCurrent(name)
    }

    fun isInDownloadingQueue(file: File): Boolean {
        return isDownloading(file) || queue.checkIsContainOnlyKey(file.name)
    }

    fun download(
        file: File,
        url: String,
        md5: String? = null,
        progress: ((totalSize: Long, completeSize: Long) -> Unit)? = null,
        callback: (succeeded: <PERSON>olean) -> Unit
    ) {
        DownloaderFactory
            .newDownloadTaskBuilder()
            .saveFile(file)
            .whenProgress { totalSize, completeSize ->
                progress?.invoke(totalSize, completeSize)
            }
            .whenComplete { succeeded: Boolean, isInterrupted: Boolean, e: Throwable?, code: Long, cleanup: IDownloadCacheCleanup ->
                if (!isInterrupted) {
                    callback.invoke(succeeded)
                }
            }
            .setQueue(queue, md5 ?: file.name, 0, 0)
            .url(url)
            .threadCount(3)
            .build()
            .startAsync()
    }

    fun stop(md5: String?, file: File? = null) {
        queue.stop(md5 ?: file?.name)
    }

    suspend fun isDownloaded(file: File, md5: String): Boolean {
        return file.exists() && file.length() > 0 && file.file2Md5Low().equals(md5, true)
    }

    suspend fun downloadSync(file: File, url: String) {
        withContext(Dispatchers.IO) {
            DownloaderFactory
                .newDownloadTaskBuilder()
                .saveFile(file)
                .setQueue(queue, file.name, 0, 0)
                .url(url)
                .threadCount(3)
                .build()
                .startSync()
        }
    }

}