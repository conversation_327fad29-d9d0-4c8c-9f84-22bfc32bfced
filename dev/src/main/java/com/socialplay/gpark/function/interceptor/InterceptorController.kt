package com.socialplay.gpark.function.interceptor

import android.os.Bundle
import com.socialplay.gpark.data.model.PlayGameInterceptor
import com.socialplay.gpark.function.analytics.resid.ResIdBean

object InterceptorController {

    const val KEY_MESSAGE = "message"
    const val KEY_GAME_NAME = "gamename"
    const val KEY_FROM = "from"
    const val KEY_IS_UGC = "isUgc"
    const val KEY_IS_MGS = "isMgs"
    const val KEY_SAVE_PLAYED_DB = "savePlayedDb"

    private val playGameInterceptorSet: HashSet<PlayGameInterceptor> by lazy { hashSetOf() }
    private val tsGameInterceptorSet: HashSet<PlayGameInterceptor> by lazy { hashSetOf() }

    /**
     * 增加玩游戏拦截器
     */
    fun addPlayGameInterceptor(interceptor: IPlayGameInterceptor, tag: String, priority: Int = InterceptorPriority.PRIORITY_DEFAULT) {
        if (tag.isNotEmpty()) {
            playGameInterceptorSet.add(PlayGameInterceptor(interceptor, priority, tag))
        }
    }

    /**
     * 增加TS游戏拦截器
     */
    fun addTsGameInterceptor(interceptor: IPlayGameInterceptor, tag: String, priority: Int = InterceptorPriority.PRIORITY_DEFAULT) {
        if (tag.isNotEmpty()) {
            tsGameInterceptorSet.add(PlayGameInterceptor(interceptor, priority, tag))
        }
    }

    /**
     * 移除玩游戏拦截器
     */
    fun removePlayGameInterceptor(tag: String) {
        if (tag.isEmpty()) {
            return
        }
        playGameInterceptorSet.removeAll { it.tag == tag }
    }


    /**
     * ts游戏拦截
     * @param tags
     *  [com.socialplay.gpark.function.mgs.MgsPlayGameTask.TAG_INTERCEPTOR_MGS]
     *  [com.socialplay.gpark.function.mgs.MgsPlayGameTask.TAG_INTERCEPTOR_UGC]
     *  [com.socialplay.gpark.function.mgs.MgsPlayGameTask.TAG_INTERCEPTOR_ANALYTIC]
     */
    suspend fun onTsGameInterceptedWithTags(
        packageName: String,
        gameId: String,
        bundle: Bundle,
        resIdBean: ResIdBean?,
        tags: Array<String>,
    ): Pair<Boolean, Bundle?> {
        if (tsGameInterceptorSet.isEmpty()) {
            return false to null
        }
        val set =
            if (tags.isEmpty()) tsGameInterceptorSet else tsGameInterceptorSet.filterTo(HashSet()) {
                tags.contains(it.tag)
            }
        return loopInterceptorSet(set, packageName, gameId, bundle, resIdBean)
    }

    /**
     * ts游戏拦截
     */
    @Deprecated("please use onTsGameInterceptedWithTags")
    suspend fun onTsGameIntercepted(packageName: String, gameId: String, bundle: Bundle, resIdBean: ResIdBean?): Pair<Boolean, Bundle?> {
        if (tsGameInterceptorSet.isEmpty()) {
            return false to null
        }
        return loopInterceptorSet(tsGameInterceptorSet, packageName, gameId, bundle, resIdBean)
    }

    /**
     * 玩游戏拦截
     */
    suspend fun onPlayGameIntercepted(packageName: String, gameId: String, bundle: Bundle?, resIdBean: ResIdBean?): Pair<Boolean, Bundle?> {
        if (playGameInterceptorSet.isEmpty()) {
            return false to null
        }
        return loopInterceptorSet(playGameInterceptorSet, packageName, gameId, bundle, resIdBean)
    }

    /**
     * 遍历玩游戏拦截器
     */
    private suspend fun loopInterceptorSet(set: HashSet<PlayGameInterceptor>, packageName: String, gameId: String, bundle: Bundle?, resIdBean: ResIdBean?): Pair<Boolean, Bundle?> {
        val interceptorSet = set.sortedByDescending { it.priority }
        var lastBundle: Bundle = bundle ?: Bundle()
        interceptorSet.forEach {
            val interceptResult = it.interceptor.onIntercept(packageName, gameId, lastBundle, resIdBean)
            val intercepted = interceptResult.first
            lastBundle = interceptResult.second
            if (intercepted) {
                return interceptResult
            }
        }
        return false to null
    }
}