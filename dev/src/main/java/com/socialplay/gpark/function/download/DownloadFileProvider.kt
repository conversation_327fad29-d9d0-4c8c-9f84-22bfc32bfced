package com.socialplay.gpark.function.download

import android.app.Application
import android.os.Build
import android.os.Environment
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.download.DownloadFileProvider.getEditorUserUnzipLocal
import org.koin.core.context.GlobalContext
import java.io.File

/**
 * create by: bin on 2021/6/4
 */
object DownloadFileProvider {

    val app: Application = GlobalContext.get().get()

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    val downloadRoot: File by lazy {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            externalDownloadRoot
        } else {
            internalDownloadRoot
        }
    }

    val externalDownloadRoot by lazy { app.getExternalFilesDir("download") ?: internalDownloadRoot }

    val internalDownloadRoot by lazy { File(app.filesDir, "download") }

    val qiNiuFileRecorder by lazy { File(internalDownloadRoot, "QiNiu${File.separator}FileRecorder") }

    // 上传压缩缓存
    val uploadCacheFolder by lazy { File(app.cacheDir, "upload_cache") }

    // oc短剧选择图片缓存
    val chooseImgCacheDir = File(app.cacheDir, "choose_image")

    // oc短剧选择图片缓存
    val shareImgCacheDir = File(app.cacheDir, "share_image")

    private val editorRoot = File(internalDownloadRoot, "editor")

    val roleScreenshotsCacheDir = File(app.cacheDir, "role_screenshots_cache")
    val gameShareCacheDir = File(app.cacheDir, "map_share")

    private val ue4File = File(app.filesDir, "UE4Game${File.separator}MetaWorldMobile${File.separator}MetaWorldMobile${File.separator}Saved${File.separator}MetaWorld${File.separator}Projects")

    private val editorLocalRoot = File(ue4File, "local")

    /**
     * @see getEditorUserUnzipLocal 代替 editorLocalUnZip , 区分用户获取本地工程
     */
    @Deprecated("需要区分用户来展示本地文件, 所以废弃")
    private val editorLocalUnZip = File(ue4File, "local")

    val playerUnZip = ue4File// 角色编辑器本地资源


    private val editorTemplateRoot = File(editorRoot, "template")

    val editorTemplateZip = File(editorTemplateRoot, "zip")

    val editorTemplateUnZip = File(editorTemplateRoot, "unzip")
    val webDownloadRoot = File(downloadRoot, "web")

    val ue4SaveImg = File(app.filesDir, "UE4Game${File.separator}MetaWorldMobile${File.separator}MetaWorldMobile${File.separator}Saved${File.separator}MetaWorld")

    val leakCanaryCacheDir by lazy { File(app.cacheDir, "leak_detect") }
    val debugExternalLeakCanaryDir by lazy { File(Environment.getExternalStorageDirectory(), "GParkLeak") }

    val appFilesDir = app.filesDir

    fun getEditorUserUnzipLocal(uuid: String = metaKV.account.uuid): File {
        return File(editorLocalUnZip, uuid)
    }
}