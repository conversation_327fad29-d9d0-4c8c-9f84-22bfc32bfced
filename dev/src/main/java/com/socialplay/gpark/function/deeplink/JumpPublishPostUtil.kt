package com.socialplay.gpark.function.deeplink

import android.content.Context
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.likelyPath
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2023/10/11
 * Desc:
 */
object JumpPublishPostUtil {

    // todo 先不做话题
    suspend fun assemblePostTagList(
        tagIdList: List<String>?,
    ): List<PostTag>? {
        val pureTagIdList = tagIdList?.mapNotNull { it.toLongOrNull() }
        if (!pureTagIdList.isNullOrEmpty()) {
            val allTag = getAllTag()
            return pureTagIdList.mapNotNull { tagId ->
                allTag?.find { it.tagId == tagId }?.tagName?.let {
                    PostTag(tagId, it)
                }
            }
        }
        return null
    }

    private suspend fun getAllTag(): List<PostTag>? {
        return kotlin.runCatching {
            GlobalContext.get().get<IMetaRepository>().getAllPostTagList()
        }.getOrNull()
    }

    fun assembleMediaList(
        context: Context,
        briefMediaList: List<String>?,
    ): List<PostMediaResource>? {
        return if (!briefMediaList.isNullOrEmpty()) {
            briefMediaList.map {
                val media = LocalMedia.generateLocalMedia(context, it)
                PostMediaResource(
                    resourceType = PostMediaResource.mimeTypeToResourceType(media.mimeType),
                    resourceValue = "",
                    thumb = null,
                    cover = null,
                    media.width,
                    media.height,
                    media.likelyPath,
                )
            }
        } else {
            null
        }
    }

    fun assembleCardList(completeCardListString: String?): List<PostCardInfo>? {
        return GsonUtil.gsonSafeParseCollection(completeCardListString)
    }
}