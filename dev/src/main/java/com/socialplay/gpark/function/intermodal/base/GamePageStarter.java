package com.socialplay.gpark.function.intermodal.base;

import android.app.Activity;
import android.content.Context;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 *     <AUTHOR> yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/03/02
 *     desc   :游戏内的View的展示
 * </pre>
 */
public class GamePageStarter {

    /**
     * Log TAG
     */
    private static final String TAG = "ModViewStarter";
    /**
     * 默认设置的传值参数，主要是给只需要一个参数的场景使用
     */
    protected static final String DEFAULT_DATA = "_GAME_PAGE_DATA_";

    /**
     * 创建跳转页面的Builder
     *
     * @param activity 当前游戏内的Activity
     * @return Builder
     */
    public static Builder create(Activity activity) {
        return new Builder(activity);
    }

    public static class Builder {

        private Map<String, Object> map;
        private final WeakReference<Activity> currentActivityRef;

        private Builder(Activity activity) {
            currentActivityRef = new WeakReference<>(activity);
        }

        /**
         * 设置默认的Data
         *
         * @param value 需要给页面传输的数据
         * @return Builder
         */
        public Builder putData(Object value) {
            put(DEFAULT_DATA, value);
            return this;
        }

        /**
         * 设置传输的数据
         *
         * @param key   给页面传输数据的Key
         * @param value 给页面传输数据的Value
         * @return Builder
         */
        public Builder put(String key, Object value) {
            if (map == null) {
                map = new HashMap<>();
            }
            map.put(key, value);
            return this;
        }

        /**
         * 设置传输的数据
         *
         * @param value 给页面传输数据的Value
         * @return Builder
         */
        public Builder put(Map<String, Object> value) {
            if (map == null) {
                map = new HashMap<>();
            }
            if (value != null) {
                map.putAll(value);
            }
            return this;
        }

        /**
         * 显示页面
         *
         * @param page 需要显示的页面
         */
        public void start(BaseGamePage page, Context context) {
            if (page == null) {
                throw new NullPointerException("page must be not null");
            }
            if (currentActivityRef != null && currentActivityRef.get() != null) {
                page.showAndData(map, currentActivityRef.get(), context);
            } else {

            }
        }

        /**
         * 显示页面
         *
         * @param page 需要显示的页面
         */
        public void start(BaseGamePage page, Context context, int gravity, int x, int y) {
            if (page == null) {
                throw new NullPointerException("page must be not null");
            }
            if (currentActivityRef != null && currentActivityRef.get() != null) {
                page.showAndData(map, currentActivityRef.get(), context, gravity, x, y);
            } else {

            }
        }
    }
}