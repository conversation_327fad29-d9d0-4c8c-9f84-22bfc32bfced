package com.socialplay.gpark.function.analytics.kernel

import com.meta.pandora.Pandora
import com.meta.pandora.data.entity.Event

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: 潘多拉发送埋点
 */
object PandoraAnalytics {
    fun track(event: Event, params: Map<String, Any>? = null) {
        Pandora.kind(event).apply { params?.let { put(params) } }.send()
    }

    fun track(event: Event, vararg pairs: Pair<String, Any>) {
        Pandora.kind(event)
            .also { wrapper ->
                if (pairs.isNotEmpty()) {
                    pairs.forEach {
                        wrapper.put(it.first, it.second)
                    }
                }
            }.send()
    }
}