package com.socialplay.gpark.function.deeplink

import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants

/**
 * Created by bo.li
 * Date: 2024/2/21
 * Desc:
 */
object DeeplinkAnalysisUtil {

    private const val KEY_PROCESS = "process"
    private const val KEY_LINK = "link"
    private const val KEY_ACTION = "action"
    private const val KEY_ERROR = "error"

    private const val PROCESS_VALUE_BUILD_URI = "build_uri"
    private const val PROCESS_VALUE_START = "start_check"
    private const val PROCESS_VALUE_PARSE_OK = "parse_ok"
    private const val PROCESS_VALUE_DISTRIBUTE_START = "distribute_start"
    private const val PROCESS_VALUE_DISTRIBUTE_ACTION = "distribute_action"
    private const val PROCESS_VALUE_DISTRIBUTE_END = "distribute_end"
    private const val PROCESS_VALUE_PUBLISH_HANDLE = "publish_handle"
    private const val PROCESS_VALUE_PUBLISH_HANDLE_END = "publish_handle_end"
    private const val PROCESS_VALUE_READY_JUMP_PUBLISH = "ready_jump_publish"


    fun start(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_START)
            put(KEY_LINK, link)
        }
    }

    fun buildUri(action: String, local: Boolean) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_BUILD_URI)
            put(KEY_ACTION, "${if (local) "local:" else ""}$action")
        }
    }

    fun failed(link: String, message: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS_FAILED) {
            put(KEY_LINK, link)
            put(KEY_ERROR, message)
        }
    }

    fun parseSuccess(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_PARSE_OK)
            put(KEY_LINK, link)
        }
    }

    fun distributeStart(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_DISTRIBUTE_START)
            put(KEY_LINK, link)
        }
    }

    fun distributeEnd(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_DISTRIBUTE_END)
            put(KEY_LINK, link)
        }
    }

    fun distributeAction(link: String, action: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_DISTRIBUTE_ACTION)
            put(KEY_LINK, link)
            put(KEY_ACTION, action)
        }
    }

    fun enterPublishHandler(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_PUBLISH_HANDLE)
            put(KEY_LINK, link)
        }
    }

    fun readyJumpPublish(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_READY_JUMP_PUBLISH)
            put(KEY_LINK, link)
        }
    }

    fun publishHandleEnd(link: String) {
        Analytics.track(EventConstants.EVENT_DYNAMIC_LINK_PROCESS) {
            put(KEY_PROCESS, PROCESS_VALUE_PUBLISH_HANDLE_END)
            put(KEY_LINK, link)
        }
    }
}