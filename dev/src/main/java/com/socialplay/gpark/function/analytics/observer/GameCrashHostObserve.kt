package com.socialplay.gpark.function.analytics.observer

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameCrashInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.dialog.DialogShowManager
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2021/5/26
 * Desc:
 */
object GameCrashHostObserve {

    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    fun initHostLifeCycleCallbacks(application: Application) {
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
            override fun onActivityStarted(activity: Activity) {}
            override fun onActivityResumed(activity: Activity) {
                checkGameCrash()
            }

            override fun onActivityPaused(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {}
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {}

        })
    }

    /**
     * 启动游戏时更新游戏崩溃记录
     */
    fun recordStartGame(packageName: String, gameId: String, isTsGame: Boolean) {
        val gameBean = GameCrashInfo(packageName, gameId = gameId, isTsGame = isTsGame)
        metaKV.analytic.updateGameCrashRecord(packageName, gameBean)
    }

    /**
     * 游戏onCreate时更新数据
     */
    fun recordGameOnCreate(packageName: String, gameId: String, isTsGame: Boolean) {
        val gameBean = metaKV.analytic.getGameCrashRecord(packageName) ?: GameCrashInfo(packageName, gameId = gameId, isTsGame = isTsGame)
        gameBean.isOnPause = false
        gameBean.isTsGame = isTsGame
        if (gameId.isNotEmpty() && gameBean.gameId != gameId) {
            gameBean.gameId = gameId
        }
        metaKV.analytic.updateGameCrashRecord(packageName, gameBean)
    }

    /**
     * 游戏onPause时更新数据
     */
    fun recordGameOnPause(packageName: String, gameId: String, isTsGame: Boolean) {
        val gameBean = metaKV.analytic.getGameCrashRecord(packageName) ?: GameCrashInfo(packageName, gameId = gameId, isTsGame = isTsGame)
        gameBean.isOnPause = true
        gameBean.isTsGame = isTsGame
        if (gameId.isNotEmpty() && gameBean.gameId != gameId) {
            gameBean.gameId = gameId
        }
        metaKV.analytic.updateGameCrashRecord(packageName, gameBean)
    }

    /**
     * 检查是否有游戏崩溃
     */
    private fun checkGameCrash() {
        Timber.d("checkGameCrash")
        val gameMap = metaKV.analytic.getGameCrashRecordMap()
        if (gameMap.isNullOrEmpty()) {
            return
        }
        var hasCrash = false
        gameMap.values.forEach {
            if (!it.isOnPause && it.pkgName.isNotEmpty()) {
                Timber.d("game：%s crash", it.pkgName)
                Analytics.track(EventConstants.EVENT_GAME_CRASH) {
                    put("packageName", it.pkgName)
                    put("pkgName", it.pkgName)
                    put("game_type", it.gameType)
                    put("gameid", it.gameId)
                }
                hasCrash = true
            }
            metaKV.analytic.removeGameCrashRecord(it.pkgName)
        }

        if (!hasCrash) {
            // 没有崩溃，可以视为正常退出游戏
            DialogShowManager.triggerGameSuccess()
        }
    }
}