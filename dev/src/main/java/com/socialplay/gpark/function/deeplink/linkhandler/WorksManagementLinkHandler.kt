package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.create.EditorCreateMineParentFragment
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.util.toIntOrZero

/**
 * GPark     gpark://gpark.fun?action=worksManagement&tab=0
 *  派对      party233://233party.com?action=worksManagement&tab=0
 *  tab参数： 0（全部）、1（已发布）、2（未发布）
 */
class WorksManagementLinkHandler : LinkHandler {
    override fun handle(
        chain: LinkHandlerChain,
        data: LinkData
    ): LinkHandleResult {
        val tabString = data.uri.getQueryParameter("tab") ?: "0"
        val tab = tabString.toIntOrZero
        MetaRouter.MobileEditor.creation(
            data.navHost,
            initTab = EditorCreateV2Fragment.TAB_MINE,
            categoryId = if (tab == 1) {
                EditorCreateMineParentFragment.TYPE_PUBLISHED
            } else if (tab == 2) {
                EditorCreateMineParentFragment.TYPE_DRAFT
            } else {
                EditorCreateMineParentFragment.TYPE_ALL
            }
        )
        return LinkHandleResult.Success
    }
}