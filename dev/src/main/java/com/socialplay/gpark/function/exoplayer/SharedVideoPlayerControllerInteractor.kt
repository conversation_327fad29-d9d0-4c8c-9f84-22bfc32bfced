package com.socialplay.gpark.function.exoplayer

import android.content.Context
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SimpleExoPlayer
import com.google.android.exoplayer2.ui.StyledPlayerView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * created by liyanfeng on 2022/10/25 6:33 下午
 * @describe:
 */
class SharedVideoPlayerControllerInteractor(val context: Context) : Player.Listener {
    companion object {
        private const val TAG = "ExoPlayer-Controller"
    }

    private val videoCacheInteractor: VideoPlayerCacheInteractor by GlobalContext.get().inject()

    private val player: ExoPlayer = SimpleExoPlayer.Builder(context)
        .setMediaSourceFactory(videoCacheInteractor.mediaSourceFactory)
        .build().apply {
            repeatMode = Player.REPEAT_MODE_ONE
            volume = 0F
            addListener(this@SharedVideoPlayerControllerInteractor)
        }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    val progressCollector = PlayerPlaybackProgressCollector(scope, player)

    // 音量控制
    private val _playerVolumeFlow: MutableStateFlow<Float> = MutableStateFlow(0F)
    val playerVolumeFlow: StateFlow<Float> = _playerVolumeFlow

    // 播放控制
    private val _playerPlaybackFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val playerPlaybackFlow: StateFlow<Boolean> = _playerPlaybackFlow

    private val _playerStateBufferingFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val playerStateBufferingFlow: StateFlow<Boolean> = _playerStateBufferingFlow

    private val _firstFrameRenderedFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val firstFrameRenderedFlow: StateFlow<Boolean> = _firstFrameRenderedFlow

    init {
        scope.launch {
            playerVolumeFlow.collect {
                player.volume = it
            }
        }

        scope.launch {
            playerPlaybackFlow.collect {
                Timber.tag(TAG).d("playerPlaybackFlow %s", it)
                player.playWhenReady = it
            }
        }
    }

    override fun onVolumeChanged(volume: Float) {
        super.onVolumeChanged(volume)
        _playerVolumeFlow.value = volume
        Timber.tag(TAG).d("onVolumeChanged %s", _playerVolumeFlow.value)
    }

    fun reverseMuteState() {
        if (_playerVolumeFlow.value == 0F) {
            _playerVolumeFlow.value = 1F
        } else {
            _playerVolumeFlow.value = 0F
        }
        Timber.tag(TAG).d("reverseMuteState %s", _playerVolumeFlow.value)
    }

    fun changeMuteState(toMute: Boolean) {
        if (_playerVolumeFlow.value == 0F && !toMute) {
            _playerVolumeFlow.value = 1F
        } else if (_playerVolumeFlow.value == 1F && toMute) {
            _playerVolumeFlow.value = 0F
        }
        Timber.tag(TAG).d("toggleMuteState %s", _playerVolumeFlow.value)
    }


    fun play() {
        _playerPlaybackFlow.value = true
        Timber.tag(TAG).d("play...")
    }

    fun pause() {
        _playerPlaybackFlow.value = false
        Timber.tag(TAG).d("pause...")
    }

    fun togglePlaybackState() {
        _playerPlaybackFlow.value = !_playerPlaybackFlow.value
        Timber.tag(TAG).d("togglePlaybackState %s", _playerPlaybackFlow.value)
    }

    fun toggleMuteState() {
        if (_playerVolumeFlow.value == 0F) {
            _playerVolumeFlow.value = 1F
        } else {
            _playerVolumeFlow.value = 0F
        }
    }

    fun mute(mute: Boolean = true) {
        _playerVolumeFlow.value = if (mute) 0F else 1F
    }


    fun isPlayWhenReady(): Boolean {
        Timber.tag(TAG).d("isPlayWhenReady %s", _playerPlaybackFlow.value)
        return _playerPlaybackFlow.value
    }

    override fun onPlayWhenReadyChanged(playWhenReady: Boolean, reason: Int) {
        _playerPlaybackFlow.value = playWhenReady
        Timber.tag(TAG).d("onPlayWhenReadyChanged playWhenReady:%s, reason:%s, playerPlaybackFlow:%s", playWhenReady, reason, _playerPlaybackFlow.value)
        if (playWhenReady) {
            progressCollector.resume()
        } else {
            progressCollector.pause()
        }
    }


    override fun onRenderedFirstFrame() {
        Timber.tag(TAG).d("onRenderedFirstFrame")
        _firstFrameRenderedFlow.value = true
    }


    override fun onPlaybackStateChanged(playbackState: Int) {
        Timber.tag(TAG).d("onPlaybackStateChanged playbackState:%s", playbackState)
        when (playbackState) {
            Player.STATE_READY     -> {

            }
            Player.STATE_BUFFERING -> {

            }
            Player.STATE_ENDED     -> {

            }
            Player.STATE_IDLE      -> {

            }
        }

        _playerStateBufferingFlow.value = playbackState == Player.STATE_BUFFERING
    }


    fun seekTo(position: Long) {
        player.seekTo(position)
        Timber.tag(TAG).d("seekTo $position")
    }

    fun setMediaItem(item: MediaItem) {
        player.setMediaItem(item)
        player.prepare()
        Timber.tag(TAG).d("setMediaItem")
    }

    fun getCurrentMediaItem(): MediaItem? {
        return player.currentMediaItem
    }

    fun clearMediaItems() {
        Timber.tag(TAG).d("clearMediaItems")
        player.clearMediaItems()
    }

    fun attachPlayerRenderView(playerView: StyledPlayerView) {
        _firstFrameRenderedFlow.value = false
        playerView.player = null
        playerView.player = player
        Timber.tag(TAG).d("attachPlayerRenderView")
    }

    suspend fun clearCache(cacheKey:String) {
        Timber.tag(TAG).d("clearCache")
        videoCacheInteractor.clearCache(cacheKey)
    }

}