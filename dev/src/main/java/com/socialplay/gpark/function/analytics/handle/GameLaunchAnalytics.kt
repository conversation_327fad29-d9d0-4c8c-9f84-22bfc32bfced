package com.socialplay.gpark.function.analytics.handle

import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import org.json.JSONObject
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: 启动游戏相关埋点
 */
object GameLaunchAnalytics {
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    private const val LAUNCH_TYPE_HOT = "hotLaunch"
    private const val LAUNCH_TYPE_COLD = "coldLaunch"

    /**
     * 启动游戏时保存、发送埋点参数
     */
    fun launchStart(
        gameId: String,
        packageName: String,
        isAlive: <PERSON><PERSON><PERSON>,
        isTsGame: <PERSON><PERSON><PERSON>,
        launchResIdBean: ResIdBean? = null,
    ) {
        val launchType = if (isAlive) LAUNCH_TYPE_HOT else LAUNCH_TYPE_COLD
        val launchTime = System.currentTimeMillis()
        metaKV.analytic.saveStartLaunchTime(packageName, launchTime)
        metaKV.analytic.saveLaunchType(packageName, launchType)
        metaKV.analytic.saveLaunchResIdBean(packageName, launchResIdBean)

        val loadingTime = System.currentTimeMillis() - (launchResIdBean?.getClickGameTime()?:0L)
        //Click只记录了TS游戏的点击时间
        val clickTime = metaKV.analytic.getClickLaunchTime(packageName)

        Analytics.track(EventConstants.EVENT_START_LAUNCH_GAME) {
            putAll(ResIdUtils.getAnalyticsMap(launchResIdBean))
            put("gameid", gameId)
            put("packageName", packageName)
            put("isalive", isAlive)
            put("launchtype", launchType)
            put("game_type", getGameType(isTsGame))
            put("mw_server_type", getMWServerType(gameId))
            put("loading_time", loadingTime)
            if (clickTime > 0) put("launchTime", (launchTime - clickTime))
        }
    }

    fun getIsFirstLaunch(pkgName: String): Boolean = metaKV.analytic.getGameFirstLaunch(pkgName)

    /**
     * 游戏启动成功时发送埋点参数
     * @return 是否可以统计
     */
    fun launchEnd(
        packageName: String,
        appName: String,
        gameId: String,
        isTsGame: Boolean,
    ): Boolean {
        val startLaunchTime = metaKV.analytic.getStartLaunchTime(packageName)
        val launchResIdBean = metaKV.analytic.getLaunchResIdBean(packageName) ?: ResIdBean()
        if (startLaunchTime <= 0) {
            return false
        }
        val launchTime = System.currentTimeMillis() - startLaunchTime
        val loadingTime = System.currentTimeMillis() - launchResIdBean.getClickGameTime()
        metaKV.analytic.saveStartLaunchTime(packageName, 0)
        val launchType = metaKV.analytic.getLaunchType(packageName)
        //Click只记录了TS游戏的点击时间
        val clickTime = metaKV.analytic.getClickLaunchTime(packageName)
        val currentTime = System.currentTimeMillis()
        Analytics.track(EventConstants.EVENT_LAUNCH_GAME_SUCCESS) {
            putAll(ResIdUtils.getAnalyticsMap(launchResIdBean))
            put("packageName", packageName)
            put("launchtime", launchTime)
            put("launchtype", launchType)
            put("game_type", getGameType(isTsGame))
            put("gameid", gameId)
            put("mw_server_type", getMWServerType(gameId))
            put("game_version_name", metaKV.analytic.getLaunchResIdBean(packageName)?.getGameVersionName() ?: "")
            put("loading_time", loadingTime)
            if (clickTime > 0) put("totalTime", currentTime - clickTime)
        }
        return true
    }

    /**
     * 游戏启动有效成功时发送埋点参数
     */
    fun launchEffective(packageName: String, appName: String, gameId: String, isTsGame: Boolean) {
        val launchType = metaKV.analytic.getLaunchType(packageName)
        val launchResIdBean = metaKV.analytic.getLaunchResIdBean(packageName) ?: ResIdBean()
        Analytics.track(EventConstants.EVENT_LAUNCH_GAME_EFFECTIVE){
            putAll(ResIdUtils.getAnalyticsMap(launchResIdBean))
            put("packageName", packageName)
            put("launchtype", launchType)
            put("game_type", getGameType(isTsGame))
            put("gameid", gameId)
            put("mw_server_type", getMWServerType(gameId))
        }
        metaKV.analytic.addPlayCount()
    }

    private fun getGameType(isTsGame: Boolean): String {
        return if (isTsGame) "ts" else "apk"
    }

    private fun getMWServerType(gameId: String): String {
        val attribute = metaKV.tsKV.getTsGameExpand(gameId).let {
            runCatching {
                JSONObject(it).optInt("attribute", 0)
            }.getOrDefault(0)
        }
        return when (attribute) {
            1 -> "ls"
            2 -> "ds"
            else -> "unknown"
        }
    }
}