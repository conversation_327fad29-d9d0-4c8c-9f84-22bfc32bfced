package com.socialplay.gpark.function.intermodal.base;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import androidx.annotation.Nullable;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public abstract class BaseGamePageHelper {

    private WeakReference<Activity> currentActivityRef;

    /**
     * 当前需要现实的数据
     */
    private Map<String, Object> currentData;

    /**
     * 保存当前页面外部传过来的数据
     *
     * @param data 数据
     */
    public void setCurrentData(Map<String, Object> data) {
        this.currentData = data;
    }

    /**
     * @return 获取当前页面保存的外部传过来的数据
     */
    protected Map<String, Object> getCurrentData() {
        return currentData != null ? currentData : new HashMap<>();
    }

    /**
     * 获取数据
     *
     * @param defaultValue 默认值
     * @param <T>          数值泛型
     * @return 获取到的值
     */
    protected <T> T getData(T defaultValue) {
        return getPageData(GamePageStarter.DEFAULT_DATA, defaultValue);
    }

    /**
     * 获取数据
     *
     * @param clz 数据的类型
     * @param <T> 数值泛型
     * @return 获取到的值
     */
    @Nullable
    protected <T> T getData(Class<T> clz) {
        return getMapData(GamePageStarter.DEFAULT_DATA, clz);
    }

    /**
     * 获取数据
     *
     * @param key          key
     * @param defaultValue 默认值
     * @param <T>          数值泛型
     * @return 获取到的值
     */
    protected <T> T getPageData(String key, T defaultValue) {
        Object value = getMapData(key, defaultValue.getClass());
        if (value != null) {
            return (T) value;
        }
        return defaultValue;
    }

    /**
     * 获取数据
     *
     * @param key        key
     * @param valueClass 目标值的类型
     * @param <T>        数值泛型
     * @return 获取到的值
     */
    @Nullable
    private <T> T getMapData(String key, Class<T> valueClass) {
        Map<String, Object> map = getCurrentData();
        if (!TextUtils.isEmpty(key) && !map.isEmpty()) {
            if (map.containsKey(key)) {
                Object value = map.get(key);
                if (value.getClass() == valueClass) {
                    return valueClass.cast(value);
                }
            }
        }
        return null;
    }

    /**
     * 保存当前的Activity
     *
     * @param activity 当前的Activity
     */
    protected void setCurrentActivity(Activity activity) {
        this.currentActivityRef = new WeakReference<>(activity);
    }

    /**
     * @return 获取当前的Activity
     */
    @Nullable
    public Activity getCurrentActivity() {
        return currentActivityRef != null ? currentActivityRef.get() : null;
    }

    /**
     * 跳转页面
     *
     * @param page 页面
     */
    public void startPage(BaseGamePage page, Context context) {
        GamePageStarter.create(getCurrentActivity()).start(page, context);
    }

    /**
     * 跳转页面
     *
     * @param page 页面
     * @param data 传输的数据
     */
    public void startPage(BaseGamePage page, Object data, Context context) {
        GamePageStarter.create(getCurrentActivity()).putData(data).start(page, context);
    }

    /**
     * 跳转页面
     *
     * @param page 页面
     * @param map  需要传输的数据
     */
    public void startPage(BaseGamePage page, Map<String, Object> map, Context context) {
        GamePageStarter.create(getCurrentActivity()).put(map).start(page, context);
    }

}
