package com.socialplay.gpark.function.gamereview

import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/28
 *     desc   :
 * </pre>
 */
object CommentAnalyticsHelper {

    fun getBindAccountSource(commentType: Int, pageType: Int): Int? {
        return when (pageType) {
            ArticleCommentInputDialogParams.CONTENT_TYPE_POST_DETAIL -> {
                when (commentType) {
                    ArticleCommentInputDialog.TYPE_PUBLISH_ARTICLE -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_POST
                    }

                    else -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT
                    }
                }
            }

            ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL -> {
                when (commentType) {
                    ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_PGC_COMMENT
                    }

                    else -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT
                    }
                }
            }

            ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL -> {
                when (commentType) {
                    ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_UGC_COMMENT
                    }

                    else -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT
                    }
                }
            }

            ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DESIGN_DETAIL -> {
                when (commentType) {
                    ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_UGC_DESIGN_COMMENT
                    }

                    else -> {
                        AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT
                    }
                }
            }

            else -> {
                null
            }
        }
    }
}