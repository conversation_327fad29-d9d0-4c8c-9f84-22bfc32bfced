package com.socialplay.gpark.function.exoplayer

import android.content.Context
import com.google.android.exoplayer2.C
import com.google.android.exoplayer2.database.StandaloneDatabaseProvider
import com.google.android.exoplayer2.source.ProgressiveMediaSource
import com.google.android.exoplayer2.upstream.DataSpec
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory
import com.google.android.exoplayer2.upstream.cache.Cache
import com.google.android.exoplayer2.upstream.cache.CacheDataSource
import com.google.android.exoplayer2.upstream.cache.CacheSpan
import com.google.android.exoplayer2.upstream.cache.CacheWriter
import com.google.android.exoplayer2.upstream.cache.ContentMetadata
import com.google.android.exoplayer2.upstream.cache.ContentMetadataMutations
import com.google.android.exoplayer2.upstream.cache.DefaultContentMetadata
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor
import com.google.android.exoplayer2.upstream.cache.SimpleCache
import com.socialplay.gpark.data.model.videofeed.WrappedVideoFeedItem
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.util.StorageUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asExecutor
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.*
import java.util.concurrent.Executor

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/07/16
 *     desc   :
 */
class VideoPlayerCacheInteractor(private val context: Context) {

    companion object {
        private const val SIZE_MB = 1024L * 1024

        private const val CACHE_MAX_SIZE: Long = 256 * SIZE_MB  //缓存大小为256M
        private const val SINGLE_CACHE_SIZE = 500 * 1024L

        // 磁盘剩余空间不足5M时，使用空缓存实现，避免数据库IO异常
        private const val CACHE_ENABLE_MIN_DISK_REQUIRE_SIZE = 5 * SIZE_MB
    }

    // 不执行缓存操作的缓存实现
    // 用于本地磁盘空间不足的情况
    private class NonCacheableImpl : Cache {
        override fun getUid(): Long {
            return Cache.UID_UNSET
        }

        override fun release() {
        }

        override fun addListener(key: String, listener: Cache.Listener): NavigableSet<CacheSpan> {
            return getCachedSpans(key)
        }

        override fun removeListener(key: String, listener: Cache.Listener) {

        }

        override fun getCachedSpans(key: String): NavigableSet<CacheSpan> {
            return TreeSet()
        }

        override fun getKeys(): Set<String> {
            return emptySet()
        }

        override fun getCacheSpace(): Long {
            return C.LENGTH_UNSET.toLong()
        }

        override fun startReadWrite(key: String, position: Long, length: Long): CacheSpan {
            return CacheSpan(key, position, length)
        }

        override fun startReadWriteNonBlocking(
            key: String,
            position: Long,
            length: Long
        ): CacheSpan? {
            return null
        }

        override fun startFile(key: String, position: Long, length: Long): File {
            return File("/dev/null")
        }

        override fun commitFile(file: File, length: Long) {
        }

        override fun releaseHoleSpan(holeSpan: CacheSpan) {
        }

        override fun removeResource(key: String) {
        }

        override fun removeSpan(span: CacheSpan) {
        }

        override fun isCached(key: String, position: Long, length: Long): Boolean {
            return true
        }

        override fun getCachedLength(key: String, position: Long, length: Long): Long {
            return length
        }

        override fun getCachedBytes(key: String, position: Long, length: Long): Long {
            return length
        }

        override fun applyContentMetadataMutations(
            key: String,
            mutations: ContentMetadataMutations
        ) {

        }

        override fun getContentMetadata(key: String): ContentMetadata {
            return DefaultContentMetadata()
        }
    }


    private val cachedDataSourceFactory: CacheDataSource.Factory by lazy {
        // SimpleCache内的数据库是异步执行，没办法简单Catch异常
        val cache = if (StorageUtils.getDataFreeSize(context) < CACHE_ENABLE_MIN_DISK_REQUIRE_SIZE) {
            NonCacheableImpl()
        } else {
            SimpleCache(
                File(context.cacheDir, "video/cache"), //设置缓存目录
                LeastRecentlyUsedCacheEvictor(CACHE_MAX_SIZE),
                StandaloneDatabaseProvider(context)
            )
        }

        CacheDataSource.Factory()
            .setUpstreamDataSourceFactory(DefaultDataSourceFactory(context))
            .setCache(cache)
    }

    private val queueOpLock = Any()
    private val cacheTaskQueue: LinkedList<CacheTaskWrapper> = LinkedList()
    private val cachingQueue: LinkedList<CacheTaskWrapper> = LinkedList()

    private val cacheThread: Executor by lazy { Dispatchers.IO.asExecutor() }

    val mediaSourceFactory by lazy {
        ProgressiveMediaSource.Factory(cachedDataSourceFactory)
    }


    fun asyncCacheVideoFeedList(
        videoList: List<WrappedVideoFeedItem>,
        type: Int,
        loadLength: Long,
        cancelExists: Boolean = false,
        onCompleted: () -> Unit = { }
    ): Int {
        // 移除没在本次新任务中出现的之前的任务
        // 移除本次中的任务已经出现在之前的任务中的

        val intersectionList = mutableListOf<CacheTaskWrapper>()
        synchronized(queueOpLock) {
            for (wrappedVideoFeedItem in videoList) {

                val filter: (CacheTaskWrapper) -> Boolean = {
                    it.url == wrappedVideoFeedItem.videoFeedItem.videoUrl && it.length == loadLength
                }

                val taskWrapper = cachingQueue.find(filter) ?: cacheTaskQueue.find(filter)

                if(taskWrapper != null){
                    intersectionList.add(taskWrapper)
                }
            }
        }

        if (cancelExists) {
            // 取消和本次新加入的不一样的任务
            synchronized(queueOpLock){
                val cachedTaskIterator = cacheTaskQueue.iterator()
                for (cacheTaskWrapper in cachedTaskIterator) {
                    if(intersectionList.find { it.url == cacheTaskWrapper.url && it.length == cacheTaskWrapper.length } == null){
                        cachedTaskIterator.remove()
                        cancelTask(cacheTaskWrapper)
                    }
                }

                val cachingTaskIterator = cachingQueue.iterator()
                for (cacheTaskWrapper in cachingTaskIterator) {
                    if(intersectionList.find { it.url == cacheTaskWrapper.url && it.length == cacheTaskWrapper.length } == null){
                        cachingTaskIterator.remove()
                        cancelTask(cacheTaskWrapper)
                    }
                }
            }
        }
        var cacheSize = 0

        videoList.forEach {

            val videoUrl = it.videoFeedItem.videoUrl
            val videoId = it.videoFeedItem.videoId
            val reqId = it.reqId ?: ""

            if(intersectionList.find { it.url == videoUrl && it.length == loadLength } == null){
                cacheSize++
                cacheVideoFeed(videoUrl, videoId, reqId, type, loadLength) {
                    onCompleted()
                }
            }
        }
        return cacheSize
    }

    suspend fun cacheVideoFeedList(
        videoList: List<WrappedVideoFeedItem>,
        type: Int,
        loadLength: Long,
        cancelExists: Boolean = false,
    ) {
        withContext(Dispatchers.IO) {
            var completeSize = 0
            val cacheSize = asyncCacheVideoFeedList(videoList, type, loadLength, cancelExists) {
                completeSize++
            }

            if (cacheSize > 0) {
                while (true) {
                    if (completeSize >= cacheSize) {
                        break
                    }
                    delay(500)
                }
            }
        }
    }

    /**
     * 缓存视频流的视频,这个方法内会自动发送缓存相关的埋点
     * 如果不需要发送埋点，可以直接调用cache方法
     */
    fun cacheVideoFeed(
        videoUrl: String,
        videoId: String,
        reqId: String,
        type: Int,
        loadLength: Long,
        onCompleted: ()-> Unit = {}
    ) {
        this.cache(
            url = videoUrl,
            position = 0,
            length = loadLength,
            callback = object : Callback {

                var loadStartTime = -1L

                override fun onStart() {
                    Timber.d("Preload video start url:${videoUrl}")
                    loadStartTime = System.currentTimeMillis()
                    Analytics.track(
                        EventConstants.EVENT_VIDEO_PRELOAD_START, mapOf(
                            "postId" to videoId,
                            "preload_type" to type,
                            "reqId" to reqId,
                        )
                    )
                }

                override fun onProgress(
                    requestLength: Long,
                    bytesCached: Long,
                    newBytesCached: Long
                ) {
                    Timber.d("Preload video progress:${bytesCached}/${requestLength} newBytesCached:${newBytesCached} url:${videoUrl}")
                }

                override fun onCancel(isStarted: Boolean) {
                    if (isStarted){
                        val cachedLength =
                            (cachedDataSourceFactory.cache?.getCachedLength(videoUrl, 0, loadLength)
                                ?: 0).coerceAtLeast(0)
                        val cachedLengthInKilobytes = cachedLength / 1024

                        Timber.d("Preload video cancel url:${videoUrl} cachedLength:${cachedLength} cachedLengthInKilobytes:${cachedLengthInKilobytes}")
                        Analytics.track(
                            EventConstants.EVENT_VIDEO_PRELOAD_ERR, mapOf(
                                "postId" to videoId,
                                "preload_type" to type,
                                "reason" to "canceled",
                                "reqId" to reqId,
                                "preload_size" to cachedLengthInKilobytes,
                            )
                        )
                    }
                    onCompleted()
                }

                override fun onCached(alreadyCached: Boolean) {
                    if (!alreadyCached){
                        val cachedLength =
                            (cachedDataSourceFactory.cache?.getCachedLength(videoUrl, 0, loadLength)
                                ?: 0).coerceAtLeast(0)
                        val cachedLengthInKilobytes = cachedLength / 1024
                        Timber.d("Preload video cached url:${videoUrl} cachedLength:${cachedLength} cachedLengthInKilobytes:${cachedLengthInKilobytes}")
                        Analytics.track(
                            EventConstants.EVENT_VIDEO_PRELOAD_FINISH, mapOf(
                                "postId" to videoId,
                                "preload_type" to type,
                                "load_timing" to (System.currentTimeMillis() - loadStartTime),
                                "reqId" to reqId,
                                "preload_size" to cachedLengthInKilobytes,
                            )
                        )
                    }
                    onCompleted()
                }

                override fun onError(e: Throwable) {
                    val cachedLength =
                        (cachedDataSourceFactory.cache?.getCachedLength(videoUrl, 0, loadLength)
                            ?: 0).coerceAtLeast(0)

                    val cachedLengthInKilobytes = cachedLength / 1024
                    Timber.d(e, "Preload video error url:${videoUrl} cachedLength:${cachedLength} cachedLengthInKilobytes:${cachedLengthInKilobytes}")
                    Analytics.track(
                        EventConstants.EVENT_VIDEO_PRELOAD_ERR, mapOf(
                            "postId" to videoId,
                            "preload_type" to type,
                            "reason" to (e.message ?: "empty"),
                            "reqId" to reqId,
                            "preload_size" to cachedLengthInKilobytes,
                        )
                    )
                    onCompleted()
                }
            })
    }


    /**
     * 缓存视频
     * @param url 需要缓存的视频的地址
     * @param position 视频缓存的开始地址
     * @param length 需要缓存多大
     * @param cancelExists 是否需要取消其他的任务
     * @param callback 用于监听缓存的状态，回调可能在子线程中调用
     */
    fun cache(
        url: String,
        position: Long = 0,
        length: Long = SINGLE_CACHE_SIZE,
        callback: Callback? = null
    ): Boolean {

        synchronized(queueOpLock){
            //默认情况下，新添加的任务优先级比较低
            cacheTaskQueue.addLast(CacheTaskWrapper(
                url = url,
                position = position,
                length = length,
                priority = (findMaxPriorityTask()?.priority ?: 0) + 1,
                callback = callback
            ))

            Timber.d("zhuwei 任务 ${url} (${position}:${length}) 入队列")
        }

        cacheInternal()

        return true
    }

    private fun cacheInternal() {
        cacheThread.execute {
            while (true) {

                val taskToExecute = synchronized(queueOpLock) {
                    val task = (cacheTaskQueue.pollFirst() ?: return@execute)

                    Timber.d("zhuwei 任务 ${task.url} (${task.position}:${task.length}) 出队列")

                    if (isCached(task.url, task.position, task.length)) {
                        Timber.d("zhuwei 任务 ${task.url} (${task.position}:${task.length}) 已经缓存过了")
                        task.callback?.onCached(true)
                        return@synchronized null
                    }

                    if (isCaching(task.url, task.position, task.length)) {
                        Timber.d("zhuwei 任务 ${task.url} (${task.position}:${task.length}) 已有任务在缓存中")
                        task.callback?.onCancel(false)
                        return@synchronized null
                    }

                    return@synchronized task.also {
                        cachingQueue.addLast(it)
                    }
                } ?: continue

                Timber.d("zhuwei 任务 ${taskToExecute.url} (${taskToExecute.position}:${taskToExecute.length}) 开始缓存")

                val dataSpec = DataSpec.Builder()
                    .setUri(taskToExecute.url)
                    .setKey(taskToExecute.url)
                    .setPosition(taskToExecute.position)
                    .setLength(taskToExecute.length)
                    .build()

                val cacheWriter = CacheWriter(
                    cachedDataSourceFactory.createDataSource(),
                    dataSpec,
                    null
                ) { requestLength, bytesCached, newBytesCached ->
                    taskToExecute.callback?.onProgress(
                        requestLength,
                        bytesCached,
                        newBytesCached
                    )
                }

                taskToExecute.cacheWriter = cacheWriter

                Timber.i("zhuwei cache 准备缓存 %s", taskToExecute.url)
                kotlin.runCatching {
                    taskToExecute.callback?.onStart()
                    cacheWriter.cache()
                    taskToExecute.cacheWriter = null
                    taskToExecute.callback?.onCached(false)
                    taskToExecute.callback = null
                    Timber.i("zhuwei cache 缓存完成 %s", taskToExecute.url)
                }.getOrElse {
                    taskToExecute.callback?.onError(it)
                    taskToExecute.callback = null
                    Timber.i("zhuwei cache 缓存失败 %s", taskToExecute.url)
                }


                synchronized(queueOpLock) {
                    cachingQueue.remove(taskToExecute)
                }
            }
        }
    }

    private fun isCaching(url: String, position: Long, length: Long): Boolean {
        synchronized(queueOpLock) {
            cachingQueue.forEach {
                if (it.url == url && it.position == position && it.length == length) {
                    return true
                }
            }
        }
        return false
    }

    private fun cancelTask(it: CacheTaskWrapper) {
        val isStarted = it.cacheWriter != null

        it.cacheWriter?.cancel()
        it.cacheWriter = null

        it.callback?.onCancel(isStarted)
        it.callback = null


        Timber.d("zhuwei 任务 ${it.url} (${it.position}:${it.length}) 被取消了 isStarted:${isStarted}")
    }

    fun cancelTasks(){
        synchronized(queueOpLock) {
            cacheTaskQueue.forEach(::cancelTask)
            cacheTaskQueue.clear()

            cachingQueue.forEach(::cancelTask)
            cachingQueue.clear()
        }
    }

    fun isCached(url: String, position: Long = 0, length: Long = 1024 * 500): Boolean {
        return cachedDataSourceFactory.cache?.isCached(url, position, length) == true
    }


    suspend fun clearCache(key: String) = withContext(Dispatchers.IO) {
        cachedDataSourceFactory.cache?.removeResource(key)
    }

    fun getCachedSizeInBytes(key: String, position: Long = 0, length: Long = 1024 * 500): Long {
        return (cachedDataSourceFactory.cache?.getCachedLength(key, position, length) ?: 0).coerceAtLeast(0)
    }

    private fun findMaxPriorityTask(): CacheTaskWrapper? = cacheTaskQueue.minByOrNull { it.priority }

    private fun findTaskByUrl(url: String): Pair<Int, CacheTaskWrapper>? {

        for (i in 0 until cacheTaskQueue.size) {
            val wrapper = cacheTaskQueue[i]
            if (wrapper.url == url) {
                return i to wrapper
            }
        }

        return null

    }

    suspend fun bringToFront(url: String, bringFollows: Boolean = true) = withContext(Dispatchers.Default) {
        synchronized(queueOpLock) {
            if(cacheTaskQueue.isEmpty()){
                return@withContext
            }

            val priorityAndTaskPair = findTaskByUrl(url)
            if (priorityAndTaskPair != null) {
                val maxPriority = findMaxPriorityTask()?.priority ?: 0
                val frontPriority = Math.min(maxPriority - 1, maxPriority)
                priorityAndTaskPair.second.priority = frontPriority

                Timber.i("调整任务:%s为前台任务,优先级设置为:%d", url, frontPriority)

                if (bringFollows) {
                    var followsPriority = frontPriority + 1
                    for (i in priorityAndTaskPair.first + 1 until cacheTaskQueue.size) {
                        Timber.i("调整任务:%s为前台任务的后续任务,优先级设置为:%d", url, followsPriority)
                        cacheTaskQueue[i].priority = followsPriority++
                    }
                }
            }
            cacheTaskQueue.sort()
            Timber.i("Tasks reordered")
        }
    }

    private data class CacheTaskWrapper(
        val url: String,
        val position: Long = 0,
        val length: Long = SINGLE_CACHE_SIZE,
        var priority: Int,
        var callback: Callback? = null,
        /*Internal use only*/var cacheWriter: CacheWriter? = null
    ) : Comparable<CacheTaskWrapper> {

        override fun compareTo(other: CacheTaskWrapper): Int {
            return priority.compareTo(other.priority)
        }
    }

    interface Callback {
        fun onStart()

        fun onProgress(
            requestLength: Long,
            bytesCached: Long,
            newBytesCached: Long
        )

        fun onCancel(isStarted: Boolean)
        fun onCached(alreadyCached: Boolean)
        fun onError(e: Throwable)
    }
}