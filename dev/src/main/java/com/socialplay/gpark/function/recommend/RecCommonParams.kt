package com.socialplay.gpark.function.recommend

import android.text.TextUtils
import com.socialplay.gpark.function.pandora.PandoraToggle


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/7/22
 *  desc   :
 */
object RecCommonParams {
    /**
     * 推荐公参
     */
    fun getRecParams() = HashMap<String, String>().apply {
        val rsConfig: String = PandoraToggle.getRsConfig()
        if (!TextUtils.isEmpty(rsConfig)) {
            put("rsConfigArr", rsConfig)
        }
        put("libra", PandoraToggle.controlRecommendLibra())
    }
}