package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext

class GroupNotificationLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val groupId =
            data.uri.getQueryParameter(MetaDeepLink.PARAM_GROUP_ID)?.toLongOrNull() ?: return LinkHandleResult.Failed("no group id")
        GlobalScope.launch {
            val item = runCatching {
                GlobalContext.get().get<IMetaRepository>().getSysHeaderInfo()
                    .singleOrNull()?.data?.find { it.groupId == groupId }
            }.getOrNull() ?: return@launch
            withContext(Dispatchers.Main) {
                MetaRouter.IM.goSys(
                    data.navHost,
                    item.groupId,
                    item.groupContentType,
                    item.title
                )
            }
        }
        return LinkHandleResult.Success
    }
}