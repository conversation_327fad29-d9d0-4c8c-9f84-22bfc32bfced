package com.socialplay.gpark.function.exoplayer

import com.google.android.exoplayer2.Player
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

class PlayerPlaybackProgressCollector(private val scope: CoroutineScope, private val player: Player) {
    private val _playbackProgress = MutableStateFlow(VideoPlaybackProgress.INVALID)
    val playbackProgress: StateFlow<VideoPlaybackProgress> = _playbackProgress

    private var collecting: AtomicBoolean = AtomicBoolean(false)

    fun cancel() = scope.launch {
        collecting.compareAndSet(true, false)
        _playbackProgress.value = VideoPlaybackProgress.INVALID
    }

    fun resume() = scope.launch {
        collecting.set(true)
        while (collecting.get()) {
            _playbackProgress.value = VideoPlaybackProgress(player.currentPosition, player.duration, player.bufferedPosition)
            delay(150)
        }
    }

    fun pause() {
        collecting.compareAndSet(true, false)
    }
}

