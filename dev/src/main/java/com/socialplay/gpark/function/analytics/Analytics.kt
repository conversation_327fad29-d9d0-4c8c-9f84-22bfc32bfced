package com.socialplay.gpark.function.analytics

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.analytics.kernel.*
import com.socialplay.gpark.function.startup.core.ProcessType
import com.meta.pandora.data.entity.Event

/**
 * Created by yaqi.liu on 2021/5/24
 */
@SuppressLint("StaticFieldLeak")
object Analytics {
    private lateinit var context: Context
    private var isMainProcess = false

    fun preInit(context: Application, processType: ProcessType) {
        PandoraInit.preInit(context, processType)
    }
    fun init(application: Application, isMainProcess: Boolean, processName: String, processType: ProcessType) {
        context = application
        this.isMainProcess = isMainProcess

        PandoraInit.init(application, isMainProcess, processType)
    }

    fun track(event: Event, block: (MutableMap<String, Any>.() -> Unit)? = null) {
        val params = mutableMapOf<String, Any>()
        params["kind_event_time"] = System.currentTimeMillis()

        block?.invoke(params)

        PandoraAnalytics.track(event, params)
    }

    fun track(event: Event, vararg params: Pair<String, Any>) {
        track(event) {
            putAll(params)
        }
    }

    fun track(event: Event, map: Map<String, Any>?) {
        track(event) {
            if (map != null) {
                putAll(map)
            }
        }
    }


}