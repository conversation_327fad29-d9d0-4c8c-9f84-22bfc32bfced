package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/01/31
 *     desc   :
 *
 */
class BindAccountAndPasswordHandler : LinkHandler {

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        var fromGameId: String? = null
        var source: String? = null
        // data 参数的传参比较复杂, 看起来像是Android本地的规则, 当进入游戏之后, 触发实名流程用的
        val dataString = data.uri.getQueryParameter("data")
        if (dataString != null) {
            val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)
            fromGameId = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_ID)
            source = arguments.getString(MetaDeepLink.PARAM_SOURCE_FROM)
        }
        // source 参数用于埋点
        if (source == null) {
            data.uri.getQueryParameter(MetaDeepLink.PARAM_SOURCE_FROM)
        }
        MetaRouter.Account.bindAccountAndPassword(
            data.navHost,
            if (source.isNullOrEmpty()) LoginPageSource.SchemeUnknown.source else source,
            fromGameId
        )
        return LinkHandleResult.Success
    }
}