package com.socialplay.gpark.function.quitgame

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.game.DelayedExitCheckRecord
import com.socialplay.gpark.data.model.game.GameQuitInfo
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.util.extension.runOnMainThreadWhenNotDestroyed
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.CancellationException

/**
 * 监听退出游戏的Observer
 */
object GameQuitObserver {

    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }
    private val repository by lazy { GlobalContext.get().get<IMetaRepository>() }
    private val scope by lazy { MainScope() }

    private var pendingGameQuitCheckJob: Job? = null

    private var pendingDialogFragment: DialogFragment? = null

    data class GameResumedEvent(
        val packageName: String,
        val gameId: String?,
        val tsGame: Boolean
    )

    private val gameQuitListeners: MutableList<IGameQuitHandler> = mutableListOf()

    init {
//        gameQuitListeners.add(GameQuitUgcHandler)
    }

    fun initHostLifecycleCallbacks(application: Application) {
        application.registerActivityLifecycleCallbacks(object :
            Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
            override fun onActivityStarted(activity: Activity) {}
            override fun onActivityResumed(activity: Activity) {
                checkGameQuitStatus(activity)
            }

            override fun onActivityPaused(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {}
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {}

        })
        CpEventBus.register(this)
    }


    /**
     * 启动游戏时调用
     */
    fun recordStartGame(
        packageName: String,
        gameId: String?,
        isTsGame: Boolean,
        isUgcGame: Boolean
    ) = scope.launch(Dispatchers.IO) {
        var displayName: String? = null
        var gameIcon: String? = null
        val gameCacheInfo: GameDetailInfo?
        if (gameId != null) {
            gameCacheInfo = repository.getGameInfoCache(gameId).data
            displayName = gameCacheInfo?.name
            gameIcon = gameCacheInfo?.icon
        }
        // 启用新游戏的时候，删除这个游戏之前的推迟退出检查记录
        metaKV.gameQuitKV.removeDelayExitCheckRecord(packageName)
        metaKV.gameQuitKV.addStartedGame(
            GameQuitInfo(
                packageName,
                gameId,
                gameIcon,
                displayName,
                isTsGame,
                isUgcGame,
                System.currentTimeMillis()
            )
        )
        if (!isUgcGame && gameId != null) {
            // apk与pgc游戏才保存
            metaKV.appraiseKV.saveGameCount(gameId)
        }
    }


    /**
     * 游戏内任意页面Resume时调用
     */
    fun recordGameResumed(packageName: String, gameId: String?, tsGame: Boolean) {
        Timber.d("recordGameResumed packageName:$packageName gameId:$gameId tsGame:$tsGame")
        CpEventBus.post(GameResumedEvent(packageName, gameId, tsGame))
    }

    /**
     * 移除游戏启动记录
     */
    fun removeStartGameRecord(packageName: String) {
        scope.launch(Dispatchers.IO) {
            metaKV.gameQuitKV.removeStartedGame(packageName)
        }
    }

    /**
     * 推迟游戏退出判断到下次游戏Resumed
     */
    fun delayExitCheckUntilGameResumedAgain(packageName: String) {
        Timber.d("delayExitCheckUntilGameResume pkg:$packageName")
        scope.launch(Dispatchers.IO) {
            metaKV.gameQuitKV.addDelayExitCheckRecord(
                DelayedExitCheckRecord(
                    packageName,
                    System.currentTimeMillis()
                )
            )
        }
    }

    private fun cancelQuitGameJob(cause: String) {
        val checkJob = pendingGameQuitCheckJob
        if (checkJob != null && checkJob.isActive) {
            checkJob.cancel(CancellationException(cause))
        }
    }


    @Subscribe
    fun onGameActiveEvent(event: GameResumedEvent) {
        Timber.d("Received GameResumedEvent packageName:${event.packageName} gameId:${event.gameId} tsGame:${event.tsGame}")
        scope.launch(Dispatchers.IO) {
            metaKV.gameQuitKV.removeDelayExitCheckRecord(event.packageName)
        }
        cancelQuitGameJob("GameQuit job canceled by game resumed event")
    }

    /**
     * 检查是否退出游戏
     */
    private fun checkGameQuitStatus(activity: Activity) {
        Timber.d("checkGameQuitStatus current activity:$activity")

        //退出到主页面才算退出游戏，因为有游戏中看广告的情况，会误触发为退出游戏
        if (activity::class.java != MainActivity::class.java) {
            Timber.d("checkGameQuitStatus current activity is not MainActivity. activity:$activity")
            return
        }

        pendingGameQuitCheckJob = scope.launch {
            delay(1500)

            // 防止切换到广告页面后也出收集弹窗
            // 只有在MainActivity在前台才出弹窗（半屏详情页会占用focus）
            if (pendingDialogFragment != null || activity.hasWindowFocus()) {
                withContext(Dispatchers.IO) {
                    val startedGames = metaKV.gameQuitKV.getStartedGames()

                    startedGames.forEach {

                        // 没有推迟退出记录才认定为真正的退出
                        if (metaKV.gameQuitKV.getDelayExitCheckRecord(it.pkg) == null) {
                            onGameQuit(activity, it, 0)
                            metaKV.gameQuitKV.removeStartedGame(it.pkg)
                        } else {
                            Timber.d("checkGameQuitStatus Quit event blocked by delay exit check record. pkg:${it.pkg}")
                        }
                    }
                }
            }
        }.apply {
            invokeOnCompletion {
                Timber.d("checkGameQuitStatus checkJobCompleted cause:$it")
            }
        }
    }

    /**
     * 发生了游戏退出
     * @param gameInfo 退出的游戏信息
     * @param quitReason 退出原因
     */
    private suspend fun onGameQuit(activity: Activity, gameInfo: GameQuitInfo, quitReason: Int) {
        Timber.d("onGameQuit activity:${activity} pkg:${gameInfo.pkg} name:${gameInfo.gameDisplayName} gameId:${gameInfo.gameId} delta:${System.currentTimeMillis() - gameInfo.startTimestamp} quitReason:${quitReason}")

        for (handler in ArrayList(gameQuitListeners)) {
            val intercepted = handler.onGameQuit(
                activity,
                pendingDialogFragment,
                gameInfo,
                quitReason
            )
            if (intercepted) {
                return
            }
        }
    }

    /**
     * 半屏pgc/ugc详情页会占用[MainActivity]的focus
     */
    fun registerDialogFragment(dialogFragment: DialogFragment) {
        dialogFragment.viewLifecycleOwner.runOnMainThreadWhenNotDestroyed {
            dialogFragment.viewLifecycleOwner.lifecycle.addObserver(object :
                DefaultLifecycleObserver {
                override fun onResume(owner: LifecycleOwner) {
                    pendingDialogFragment = dialogFragment
                }

                override fun onPause(owner: LifecycleOwner) {
                    pendingDialogFragment = null
                }

                override fun onDestroy(owner: LifecycleOwner) {
                    dialogFragment.viewLifecycleOwner.lifecycle.removeObserver(this)
                }
            })
        }
    }

}