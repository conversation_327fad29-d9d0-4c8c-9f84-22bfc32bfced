package com.socialplay.gpark.function.deeplink

import android.net.Uri
import android.os.Bundle
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.extension.filterValueNotNull
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.getSharedViewModel
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/13
 *     desc   :
 * </pre>
 */
object ShareActiveHelper : IDialogManager {

    private val metaKv by lazy { GlobalContext.get().get<MetaKV>() }
    private val repository by lazy { GlobalContext.get().get<IMetaRepository>() }
    private var jumpUri: Uri? = null
    private var extras: String? = null

    var jumpMain = false

    var shouldProceedRelayData: Boolean? = null
            get() {
                if (field == null) {
                    field = metaKv.appKV.shareRelayDataProcessTime <= 0
                }
                return field
            }

    var needCheck = MetaDeepLink.ENABLE_SHARE_ACTIVATION
        get() {
            val temp = field
            field = false
            return temp
        }
        private set

    fun skipCheck() {
        needCheck = false
    }

    fun updateTime() {
        metaKv.appKV.shareRelayDataProcessTime = System.currentTimeMillis()
        shouldProceedRelayData = false
    }

    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        finishCallback.invoke(true)
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        if (shouldProceedRelayData != true || !needCheck
            || DialogShowManager.getShownCountByScene(DialogScene.MAIN_PAGE) > 0
        ) {
            needShowCallback(false)
            return
        }
        fragment.lifecycleScope.launch {
            val relay = repository.queryShareRelayData(WebUtil.getUserAgent())
            if (!relay.succeeded) {
                needShowCallback(false)
                return@launch
            }
            if (relay.data == null) {
                needShowCallback(false)
                return@launch
            }
            val data = relay.data
            val uri = runCatching { data?.scheme?.toUri() }.getOrNull()
            if (uri == null) {
                needShowCallback(false)
                return@launch
            }
            jumpUri = uri
            extras = data?.extras
            needShowCallback(true)
        }
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        if (!fragment.isAdded || fragment.isDetached) {
            onDismissCallback.invoke(true)
            return
        }
        jumpUri?.let {
            MetaDeepLink.handle(
                fragment.requireActivity(),
                fragment,
                fragment.getSharedViewModel<MainViewModel>(),
                it,
                LinkData.SOURCE_SHARE_DATA_RELAY
            )
            onDismissCallback.invoke(false)
        } ?: run {
            onDismissCallback.invoke(true)
        }

        Analytics.track(EventConstants.EVENT_SHARE_ACTIVE, buildMap {
            GsonUtil.gsonSafeParse<Map<String, Any?>>(extras)?.filterValueNotNull()?.let {
                putAll(it)
            }
        })
        updateTime()
    }

    override fun exeDismiss() {

    }
}