/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.socialplay.gpark.function.navigation

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.annotation.CallSuper
import androidx.core.content.res.use
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentManager.OnBackStackChangedListener
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.CreationExtras
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.NavigatorProvider
import androidx.navigation.NavigatorState
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.R
import com.socialplay.gpark.BuildConfig
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * Navigator that navigates through [fragment transactions][FragmentTransaction]. Every destination
 * using this Navigator must set a valid Fragment class name with `android:name` or
 * [Destination.setClassName].
 *
 * The current Fragment from FragmentNavigator's perspective can be retrieved by calling
 * [FragmentManager.getPrimaryNavigationFragment] with the FragmentManager passed to this
 * FragmentNavigator.
 *
 * Note that the default implementation does Fragment transactions asynchronously, so the current
 * Fragment will not be available immediately (i.e., in callbacks to
 * [NavController.OnDestinationChangedListener]).
 *
 * FragmentNavigator respects [Log.isLoggable] for debug logging, allowing you to use `adb shell
 * setprop log.tag.FragmentNavigator VERBOSE`.
 */
@Navigator.Name("fragment")
public open class FragmentNavigatorPlus(
    private val context: Context, private val fragmentManager: FragmentManager, private val containerId: Int
) : Navigator<FragmentNavigatorPlus.Destination>() {
    // Logging for FragmentNavigator is automatically enabled along with FragmentManager logging.
    // see more at [Debug your fragments][https://developer.android.com/guide/fragments/debugging]
    private fun isLoggingEnabled(level: Int): Boolean {
        return Log.isLoggable("FragmentManager", level) || Log.isLoggable(TAG, level) || BuildConfig.DEBUG
    }

    private val savedIds = mutableSetOf<String>()

    /**
     * A list of pending operations within a Transaction expected to be executed by FragmentManager.
     * Pending ops are added at the start of a transaction, and by the time a transaction completes,
     * this list is expected to be cleared.
     *
     * In general, each entry would be added only once to this list within a single transaction
     * except in the case of singleTop transactions. Single top transactions involve two fragment
     * instances with the same entry, so we would get two onBackStackChanged callbacks on the same
     * entry.
     *
     * Each Pair represents the entry.id and whether this entry is getting popped
     */
    internal val pendingOps = mutableListOf<Pair<String, Boolean>>()

    /** Get the back stack from the [state]. */
    internal val backStack
        get() = state.backStack

    /**
     * 获取Fragment的简化名称（去掉包名）
     */
    private fun getFragmentSimpleName(fragment: Fragment): String {
        return fragment.javaClass.simpleName
    }

    /**
     * 获取Fragment的简化名称（去掉包名）
     */
    private fun getFragmentSimpleName(className: String): String {
        return className.substringAfterLast('.')
    }

    /**
     * 简化entry的id（只取前8位）
     */
    private fun getSimplifiedEntryId(entryId: String): String {
        return if (entryId.length > 8) entryId.substring(0, 8) else entryId
    }

    /**
     * 格式化entry信息：Fragment名字(简化id)
     */
    private fun formatEntryInfo(entry: NavBackStackEntry): String {
        val destination = entry.destination as Destination
        val fragmentName = getFragmentSimpleName(destination.className)
        val simplifiedId = getSimplifiedEntryId(entry.id)
        return "$fragmentName($simplifiedId)"
    }

    /**
     * 格式化entry列表信息
     */
    private fun formatEntryListInfo(entries: List<NavBackStackEntry>): String {
        return entries.joinToString(", ") { formatEntryInfo(it) }
    }

    /**
     * 格式化entry id列表信息（包含Fragment名字）
     */
    private fun formatEntryIdListInfo(entryIds: List<String>): String {
        return entryIds.joinToString(", ") { id ->
            val entry = (state.backStack.value + state.transitionsInProgress.value).find { it.id == id }
            if (entry != null) {
                formatEntryInfo(entry)
            } else {
                id
            }
        }
    }

    /**
     * 获取FragmentManager的简化栈信息（活跃Fragment）
     */
    private fun getFragmentManagerStackInfo(): String {
        return fragmentManager.fragments.joinToString(", ") { fragment ->
            val fragmentName = getFragmentSimpleName(fragment)
            val tag = fragment.tag ?: "null"
            val simplifiedTag = getSimplifiedEntryId(tag)
            val lifecycleState = fragment.lifecycle.currentState.name
            "$fragmentName($simplifiedTag)[$lifecycleState]"
        }
    }

    /**
     * 获取FragmentManager的完整回退栈信息
     */
    private fun getFragmentManagerBackStackInfo(): String {
        val backStackEntries = mutableListOf<String>()
        for (i in 0 until fragmentManager.backStackEntryCount) {
            try {
                val entry = fragmentManager.getBackStackEntryAt(i)
                val name = entry.name ?: "null"
                val simplifiedName = getSimplifiedEntryId(name)

                // 尝试通过Navigation state栈获取Fragment名字
                val navEntry = (state.backStack.value + state.transitionsInProgress.value).find { it.id == name }
                val fragmentName = if (navEntry != null) {
                    getFragmentSimpleName((navEntry.destination as Destination).className)
                } else {
                    // 如果Navigation state栈中找不到，尝试通过FragmentManager的fragments列表获取
                    val fragment = fragmentManager.fragments.find { it.tag == name }
                    if (fragment != null) {
                        getFragmentSimpleName(fragment)
                    } else {
                        "unknown"
                    }
                }

                backStackEntries.add("$fragmentName($simplifiedName)")
            } catch (e: Exception) {
                backStackEntries.add("entry$i(error)")
            }
        }
        return backStackEntries.joinToString(", ")
    }

    /**
     * 检测FragmentManager状态异常
     */
    private fun detectFragmentManagerStateAnomaly(operation: String): String? {
        val activeFragments = fragmentManager.fragments
        val backStackNames = mutableListOf<String>()
        for (i in 0 until fragmentManager.backStackEntryCount) {
            try {
                val entry = fragmentManager.getBackStackEntryAt(i)
                backStackNames.add(entry.name ?: "null")
            } catch (e: Exception) {
                backStackNames.add("error")
            }
        }

        val navStackIds = state.backStack.value.map { it.id }
        val navTransitionsIds = state.transitionsInProgress.value.map { it.id }
        val allNavIds = navStackIds + navTransitionsIds

        // 检查活跃Fragment是否都在Navigation栈中
        val orphanFragments = activeFragments.filter { fragment ->
            val tag = fragment.tag
            tag != null && !allNavIds.contains(tag)
        }

        // 检查回退栈中的Fragment是否都在Navigation栈中
        val orphanBackStackEntries = backStackNames.filter { name ->
            name != "null" && !allNavIds.contains(name)
        }

        if (orphanFragments.isNotEmpty() || orphanBackStackEntries.isNotEmpty()) {
            val anomalyInfo = StringBuilder()
            anomalyInfo.append("检测到FragmentManager状态异常: $operation\n")

            if (orphanFragments.isNotEmpty()) {
                anomalyInfo.append("孤儿活跃Fragment: ")
                anomalyInfo.append(orphanFragments.joinToString(", ") {
                    "${getFragmentSimpleName(it)}(${it.tag})"
                })
                anomalyInfo.append("\n")
            }

            if (orphanBackStackEntries.isNotEmpty()) {
                anomalyInfo.append("孤儿回退栈条目: ")
                anomalyInfo.append(orphanBackStackEntries.joinToString(", ") {
                    getSimplifiedEntryId(it)
                })
                anomalyInfo.append("\n")
            }

            anomalyInfo.append("Navigation栈: ${navStackIds.joinToString(", ") { getSimplifiedEntryId(it) }}\n")
            anomalyInfo.append("Navigation转换中: ${navTransitionsIds.joinToString(", ") { getSimplifiedEntryId(it) }}\n")
            anomalyInfo.append(
                "FragmentManager活跃栈: ${
                    activeFragments.joinToString(", ") {
                        "${getFragmentSimpleName(it)}(${it.tag ?: "null"})"
                    }
                }\n")
            anomalyInfo.append("FragmentManager回退栈: ${backStackNames.joinToString(", ") { getSimplifiedEntryId(it) }}")

            return anomalyInfo.toString()
        }

        return null
    }

    /**
     * 格式化pendingOps信息
     */
    private fun formatPendingOpsInfo(): String {
        return pendingOps.joinToString(", ") { (id, isPop) ->
            val entry = (state.backStack.value + state.transitionsInProgress.value).find { it.id == id }
            if (entry != null) {
                val fragmentName = getFragmentSimpleName((entry.destination as Destination).className)
                val simplifiedId = getSimplifiedEntryId(id)
                "$fragmentName($simplifiedId)${if (isPop) "[pop]" else ""}"
            } else {
                "${getSimplifiedEntryId(id)}${if (isPop) "[pop]" else ""}"
            }
        }
    }

    private val fragmentObserver = LifecycleEventObserver { source, event ->
        if (event == Lifecycle.Event.ON_DESTROY) {
            val fragment = source as Fragment
            val entry = state.transitionsInProgress.value.lastOrNull { entry -> entry.id == fragment.tag }
            if (isLoggingEnabled(Log.VERBOSE)) {
                val fragmentName = getFragmentSimpleName(fragment)
                Timber.tag(TAG).d("fragmentObserver Fragment销毁: $fragmentName, event=$event, entry=${entry?.let { formatEntryInfo(it) }}")
            }
            if (entry != null) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("fragmentObserver 标记转换完成: ${formatEntryInfo(entry)}, 原因: Fragment生命周期达到DESTROYED")
                }
                state.markTransitionComplete(entry)
            }
        }
    }

    private val fragmentViewObserver = { entry: NavBackStackEntry ->
        LifecycleEventObserver { owner, event ->
            // Once the lifecycle reaches RESUMED, if the entry is in the back stack we can mark
            // the transition complete
            if (event == Lifecycle.Event.ON_RESUME && state.backStack.value.contains(entry)) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("fragmentViewObserver resume 标记转换完成: ${formatEntryInfo(entry)}, 原因: Fragment视图生命周期达到RESUMED")
                }
                state.markTransitionComplete(entry)
            }
            // Once the lifecycle reaches DESTROYED, we can mark the transition complete
            if (event == Lifecycle.Event.ON_DESTROY) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("fragmentViewObserver 销毁 标记转换完成: ${formatEntryInfo(entry)}, 原因: Fragment视图生命周期达到DESTROYED")
                }
                state.markTransitionComplete(entry)
            }
        }
    }

    override fun onAttach(state: NavigatorState) {
        super.onAttach(state)
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("onAttach 导航器附加到状态")
        }

        fragmentManager.addFragmentOnAttachListener { _, fragment ->
            val entry = state.backStack.value.lastOrNull { it.id == fragment.tag }
            if (isLoggingEnabled(Log.VERBOSE)) {
                val fragmentName = getFragmentSimpleName(fragment)
                Timber.tag(TAG).d("onAttach Fragment附加: $fragmentName, entry=${entry?.let { formatEntryInfo(it) }}")

                // 检查是否是意外的Fragment附加
                if (entry == null) {
                    val navStackIds = state.backStack.value.map { it.id }
                    val navTransitionsIds = state.transitionsInProgress.value.map { it.id }
                    val allNavIds = navStackIds + navTransitionsIds

                    if (!allNavIds.contains(fragment.tag)) {
                        Timber.tag(TAG).w("onAttach 检测到意外的Fragment附加: $fragmentName(${fragment.tag}), Navigation栈=[${navStackIds.joinToString(", ") { getSimplifiedEntryId(it) }}], 转换中=[${navTransitionsIds.joinToString(", ") { getSimplifiedEntryId(it) }}]")
                    }
                }
            }

            if (entry != null) {
                attachObservers(entry, fragment)
                // We need to ensure that if the fragment has its state saved and then that state
                // later cleared without the restoring the fragment that we also clear the state
                // of the associated entry.
                attachClearViewModel(fragment, entry, state)
            }
        }

        fragmentManager.addOnBackStackChangedListener(object : OnBackStackChangedListener {
            override fun onBackStackChanged() {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("onBackStackChanged 回退栈变化: pendingOps=[${formatPendingOpsInfo()}], state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}], savedIds=${savedIds}")

                    // 检测FragmentManager状态异常
                    val anomalyInfo = detectFragmentManagerStateAnomaly("onBackStackChanged")
                    if (anomalyInfo != null) {
                        Timber.tag(TAG).w("onBackStackChanged $anomalyInfo")
                    }

                    // 打印NavController的backQueue信息
                    try {
                        Timber.tag(TAG).d("onBackStackChanged 开始打印NavController状态")
                        NavControllerDebugHelper.printNavControllerStateFromFragmentManager(fragmentManager, context, "onBackStackChanged")
                    } catch (e: Exception) {
                        Timber.tag(TAG).e("onBackStackChanged 打印NavController状态失败: ${e.message}")
                        e.printStackTrace()
                    }
                }

                // 清理过期的pending operations
                cleanupStalePendingOps()

                // 验证并同步状态
                validateAndSyncState("onBackStackChanged")

                // 使用新的状态恢复工具进行智能检查
                try {
                    // 通过NavHostFragment获取NavController
                    val navHostFragment = fragmentManager.findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
                    val navController = navHostFragment?.navController
                    if (navController != null) {
                        NavigationStateRecovery.checkAndFixStateInconsistency(navController, fragmentManager)
                    }
                } catch (e: Exception) {
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).w(e, "onBackStackChanged 状态恢复检查失败")
                    }
                }

                // 检查是否需要强制状态恢复
                checkAndForceStateRecovery("onBackStackChanged")
            }

            override fun onBackStackChangeStarted(fragment: Fragment, pop: Boolean) {
                // We only care about the pop case here since in the navigate case by the time
                // we get here the fragment will have already been moved to STARTED.
                // In the case of a pop, we move the entries to STARTED
                if (isLoggingEnabled(Log.VERBOSE)) {
                    val fragmentName = getFragmentSimpleName(fragment)
                    Timber.tag(TAG).d("onBackStackChangeStarted 回退栈变化开始: $fragmentName, pop=$pop, pendingOps=[${formatPendingOpsInfo()}], state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")

                    // 检测FragmentManager状态异常
                    val anomalyInfo = detectFragmentManagerStateAnomaly("onBackStackChangeStarted")
                    if (anomalyInfo != null) {
                        Timber.tag(TAG).w("onBackStackChangeStarted $anomalyInfo")
                    }
                }

                if (pop) {
                    // 首先尝试通过fragment.tag查找
                    var entry = state.backStack.value.lastOrNull { it.id == fragment.tag }

                    // 如果找不到，尝试通过Fragment类型查找（兼容性处理）
                    if (entry == null) {
                        val fragmentClassName = fragment.javaClass.name
                        entry = state.backStack.value.lastOrNull {
                            (it.destination as Destination).className == fragmentClassName
                        }

                        // 如果通过类型找到了entry，更新Fragment的tag以保持一致性
                        if (entry != null && isLoggingEnabled(Log.VERBOSE)) {
                            Timber.tag(TAG).w("onBackStackChangeStarted 通过类型找到entry，tag不匹配: fragment.tag=${fragment.tag}, entry.id=${entry.id}, 将同步状态")
                        }
                    }

                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("onBackStackChangeStarted 准备转换: fragment.tag=${fragment.tag}, entry=${entry?.let { formatEntryInfo(it) }}")
                        if (entry == null) {
                            val allEntryIds = state.backStack.value.map { "${getFragmentSimpleName((it.destination as Destination).className)}(${getSimplifiedEntryId(it.id)})" }
                            Timber.tag(TAG).w("onBackStackChangeStarted 找不到entry: fragment.tag=${fragment.tag}, fragment.class=${fragment.javaClass.simpleName}, 可用entries=[${allEntryIds.joinToString(", ")}]")
                        }
                    }
                    entry?.let { state.prepareForTransition(it) }
                }
            }

            override fun onBackStackChangeCommitted(fragment: Fragment, pop: Boolean) {
                // 首先尝试通过fragment.tag查找
                var entry = (state.backStack.value + state.transitionsInProgress.value).lastOrNull {
                    it.id == fragment.tag
                }

                // 如果找不到，尝试通过Fragment类型查找（兼容性处理）
                if (entry == null) {
                    val fragmentClassName = fragment.javaClass.name
                    entry = (state.backStack.value + state.transitionsInProgress.value).lastOrNull {
                        (it.destination as Destination).className == fragmentClassName
                    }
                }

                // In case of system back, all pending transactions are executed before handling
                // back press, hence pendingOps will be empty.
                val isSystemBack = pop && pendingOps.isEmpty() && fragment.isRemoving
                val op = pendingOps.firstOrNull { it.first == fragment.tag }
                op?.let { pendingOps.remove(it) }

                val popOp = op?.second == true

                if (isLoggingEnabled(Log.VERBOSE)) {
                    val fragmentName = getFragmentSimpleName(fragment)
                    Timber.tag(TAG)
                        .d("onBackStackChangeCommitted 回退栈变化提交: $fragmentName, pop=$pop, entry=${entry?.let { formatEntryInfo(it) }}, isSystemBack=$isSystemBack, popOp=$popOp, pendingOps=[${formatPendingOpsInfo()}], state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")

                    // 检测FragmentManager状态异常
                    val anomalyInfo = detectFragmentManagerStateAnomaly("onBackStackChangeCommitted")
                    if (anomalyInfo != null) {
                        Timber.tag(TAG).w("onBackStackChangeCommitted $anomalyInfo")
                    }
                }

                // 修复：只有当entry为null且不是pop操作时才记录错误
                if (entry == null && !pop && !popOp) {
                    Timber.tag(TAG).e(
                        "onBackStackChangeCommitted entry is null, The fragment $fragment is unknown to the FragmentNavigator. " +
                                "Please use the navigate() function to add fragments to the FragmentNavigator managed FragmentManager."
                    )
                }

                if (entry != null) {
                    // In case we get a fragment that was never attached to the fragment
                    // manager,
                    // we need to make sure we still return the entries to their proper final
                    // state.
                    attachClearViewModel(fragment, entry, state)
                    // This is the case of system back where we will need to make the call to
                    // popBackStack. Otherwise, popBackStack was called directly and we avoid
                    // popping again.
                    if (isSystemBack) {
                        if (isLoggingEnabled(Log.VERBOSE)) {
                            Timber.tag(TAG).d("onBackStackChangeCommitted 系统返回: entry=${formatEntryInfo(entry)}")
                        }
                        state.popWithTransition(entry, false)
                    }
                }

                // 在系统返回后，强制进行状态同步检查
                if (isSystemBack) {
                    // 延迟执行状态验证，确保所有Fragment状态变化都已完成
                    fragment.view?.post {
                        validateAndSyncState("onBackStackChangeCommitted-systemBack")
                    }
                }
            }
        })
    }

    private fun attachObservers(entry: NavBackStackEntry, fragment: Fragment) {
        if (isLoggingEnabled(Log.VERBOSE)) {
            val fragmentName = getFragmentSimpleName(fragment)
            Timber.tag(TAG).d("attachObservers 附加观察者: ${formatEntryInfo(entry)}, $fragmentName")
        }
        fragment.viewLifecycleOwnerLiveData.observe(fragment) { owner ->
            // attach observer unless it was already popped at this point
            // we get onBackStackStackChangedCommitted callback for an executed navigate where we
            // remove incoming fragment from pendingOps before ATTACH so the listener will still
            // be added
            val isPending = pendingOps.any { it.first == fragment.tag }
            if (isLoggingEnabled(Log.VERBOSE)) {
                val fragmentName = getFragmentSimpleName(fragment)
                Timber.tag(TAG).d("attachObservers 视图生命周期: $fragmentName, isPending=$isPending, owner=$owner, entry=${formatEntryInfo(entry)}")
            }
            if (owner != null && !isPending) {
                val viewLifecycle = fragment.viewLifecycleOwner.lifecycle
                // We only need to add observers while the viewLifecycle has not reached a final
                // state
                if (viewLifecycle.currentState.isAtLeast(Lifecycle.State.CREATED)) {
                    viewLifecycle.addObserver(fragmentViewObserver(entry))
                }
            }
        }
        fragment.lifecycle.addObserver(fragmentObserver)
    }

    internal fun attachClearViewModel(
        fragment: Fragment, entry: NavBackStackEntry, state: NavigatorState
    ) {
        if (isLoggingEnabled(Log.VERBOSE)) {
            val fragmentName = getFragmentSimpleName(fragment)
            Timber.tag(TAG).d("attachClearViewModel 附加清除ViewModel: $fragmentName, ${formatEntryInfo(entry)}")
        }
        val viewModel = ViewModelProvider(
            fragment.viewModelStore, viewModelFactory { initializer { ClearEntryStateViewModel() } }, CreationExtras.Empty
        )[ClearEntryStateViewModel::class.java]
        viewModel.completeTransition = WeakReference {
            entry.let {
                state.transitionsInProgress.value.forEach { entry ->
                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("attachClearViewModel 标记转换完成: ${formatEntryInfo(entry)}, 原因: Fragment ViewModel被清除")
                    }
                    state.markTransitionComplete(entry)
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     *
     * This method must call [FragmentTransaction.setPrimaryNavigationFragment] if the pop succeeded
     * so that the newly visible Fragment can be retrieved with
     * [FragmentManager.getPrimaryNavigationFragment].
     *
     * Note that the default implementation pops the Fragment asynchronously, so the newly visible
     * Fragment from the back stack is not instantly available after this call completes.
     */
    override fun popBackStack(popUpTo: NavBackStackEntry, savedState: Boolean) {
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).d("popBackStack 忽略调用: FragmentManager已保存状态")
            return
        }
        val beforePopList = state.backStack.value
        // Get the set of entries that are going to be popped
        val popUpToIndex = beforePopList.indexOf(popUpTo)
        val poppedList = beforePopList.subList(popUpToIndex, beforePopList.size)
        val initialEntry = beforePopList.first()

        // 安全检查：防止意外弹出重要的Fragment
        if (poppedList.size > 1 && isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).w("popBackStack 安全检查: 即将弹出${poppedList.size}个entries")

            // 检查是否包含MainFragment
            val hasMainFragment = poppedList.any { entry ->
                val destination = entry.destination as? Destination
                val className = destination?.className
                className?.contains("MainFragment") == true
            }

            if (hasMainFragment) {
                Timber.tag(TAG).w("popBackStack 安全检查: 检测到即将弹出MainFragment，这可能不安全")
                Timber.tag(TAG).w("popBackStack 安全检查: 被弹出的entries: ${poppedList.map { formatEntryInfo(it) }}")

                // 可以在这里添加额外的保护逻辑
                // 例如：如果检测到不安全的操作，可以修改popUpTo或者取消操作
            }
        }

        // add pending ops here before any animation (if present) or FragmentManager work starts
        val incomingEntry = beforePopList.elementAtOrNull(popUpToIndex - 1)
        if (incomingEntry != null) {
            addPendingOps(incomingEntry.id)
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("popBackStack 添加入栈操作: ${formatEntryInfo(incomingEntry)}, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
            }
        }
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG)
                .d("popBackStack 出栈操作: popUpTo=${formatEntryInfo(popUpTo)}, savedState=$savedState, 出栈前栈=[${formatEntryListInfo(beforePopList)}], popUpToIndex=$popUpToIndex, 将被弹出=[${formatEntryListInfo(poppedList)}], 入栈项=${incomingEntry?.let { formatEntryInfo(it) }}, FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")

            // 打印NavController的backQueue信息
            try {
//                Timber.tag(TAG).d("popBackStack 开始打印NavController状态")
                NavControllerDebugHelper.printNavControllerStateFromFragmentManager(fragmentManager, context, "popBackStack")
            } catch (e: Exception) {
                Timber.tag(TAG).e("popBackStack 打印NavController状态失败: ${e.message}")
                e.printStackTrace()
            }
        }

        // 添加被pop的entries到pendingOps
        poppedList.filter { entry ->
            // normally we don't add initialEntry to pending ops because the adding/popping
            // of an isolated fragment does not trigger onBackStackCommitted. But if initial
            // entry was already added to pendingOps, it was likely an incomingEntry that now
            // needs to be popped, so we need to overwrite isPop to true here.
            pendingOps.asSequence().map { it.first }.contains(entry.id) || entry.id != initialEntry.id
        }.forEach { entry -> addPendingOps(entry.id, isPop = true) }
        if (savedState) {
            // Now go through the list in reversed order (i.e., started from the most added)
            // and save the back stack state of each.
            for (entry in poppedList.reversed()) {
                if (entry == initialEntry) {
                    Timber.tag(TAG).i("popBackStack FragmentManager无法保存初始目标状态: ${formatEntryInfo(entry)}")
                } else {
                    fragmentManager.saveBackStack(entry.id)
                    savedIds += entry.id
                }
            }
        } else {
            fragmentManager.popBackStack(popUpTo.id, FragmentManager.POP_BACK_STACK_INCLUSIVE)
        }
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("popBackStack 执行出栈转换: ${formatEntryInfo(popUpTo)}, savedState=$savedState, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
            // 打印NavController的backQueue信息
            try {
                Timber.tag(TAG).d("popBackStack 开始打印NavController状态")
                NavControllerDebugHelper.printNavControllerStateFromFragmentManager(fragmentManager, context, "popBackStack")
            } catch (e: Exception) {
                Timber.tag(TAG).e("popBackStack 打印NavController状态失败: ${e.message}")
                e.printStackTrace()
            }
        }
        state.popWithTransition(popUpTo, savedState)

        if (isLoggingEnabled(Log.VERBOSE)) {
            // 打印NavController的backQueue信息
            try {
                Timber.tag(TAG).d("popBackStack 开始打印NavController状态")
                NavControllerDebugHelper.printNavControllerStateFromFragmentManager(fragmentManager, context, "popBackStack")
            } catch (e: Exception) {
                Timber.tag(TAG).e("popBackStack 打印NavController状态失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    public override fun createDestination(): Destination {
        return Destination(this)
    }

    /**
     * Instantiates the Fragment via the FragmentManager's [androidx.fragment.app.FragmentFactory].
     *
     * Note that this method is **not** responsible for calling [Fragment.setArguments] on the
     * returned Fragment instance.
     *
     * @param context Context providing the correct [ClassLoader]
     * @param fragmentManager FragmentManager the Fragment will be added to
     * @param className The Fragment to instantiate
     * @param args The Fragment's arguments, if any
     * @return A new fragment instance.
     */
    @Suppress("DeprecatedCallableAddReplaceWith")
    @Deprecated(
        """Set a custom {@link androidx.fragment.app.FragmentFactory} via
      {@link FragmentManager#setFragmentFactory(FragmentFactory)} to control
      instantiation of Fragments."""
    )
    public open fun instantiateFragment(
        context: Context, fragmentManager: FragmentManager, className: String, args: Bundle?
    ): Fragment {
        return fragmentManager.fragmentFactory.instantiate(context.classLoader, className)
    }

    /**
     * {@inheritDoc}
     *
     * This method should always call [FragmentTransaction.setPrimaryNavigationFragment] so that the
     * Fragment associated with the new destination can be retrieved with
     * [FragmentManager.getPrimaryNavigationFragment].
     *
     * Note that the default implementation commits the new Fragment asynchronously, so the new
     * Fragment is not instantly available after this call completes.
     *
     * This call will be ignored if the FragmentManager state has already been saved.
     */
    override fun navigate(
        entries: List<NavBackStackEntry>, navOptions: NavOptions?, navigatorExtras: Navigator.Extras?
    ) {
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).d("navigate 忽略调用: FragmentManager已保存状态")
            return
        }
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("navigate 导航操作: entries=[${formatEntryListInfo(entries)}], navOptions=$navOptions, navigatorExtras=$navigatorExtras, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")

            // 打印NavController的backQueue信息
            try {
                Timber.tag(TAG).d("navigate 开始打印NavController状态")
                NavControllerDebugHelper.printNavControllerStateFromFragmentManager(fragmentManager, context, "navigate")
            } catch (e: Exception) {
                Timber.tag(TAG).e("navigate 打印NavController状态失败: ${e.message}")
                e.printStackTrace()
            }
        }

        // 在导航前验证状态
        validateAndSyncState("navigate")
        for (entry in entries) {
            navigate(entry, navOptions, navigatorExtras)
        }
    }

    private fun navigate(
        entry: NavBackStackEntry, navOptions: NavOptions?, navigatorExtras: Navigator.Extras?
    ) {
        val initialNavigation = state.backStack.value.isEmpty()
        val restoreState = (navOptions != null && !initialNavigation && navOptions.shouldRestoreState() && savedIds.remove(entry.id))
        if (restoreState) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("navigate 恢复状态: ${formatEntryInfo(entry)}, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
            }
            // Restore back stack does all the work to restore the entry
            fragmentManager.restoreBackStack(entry.id)
            state.pushWithTransition(entry)
            return
        }
        val ft = createFragmentTransaction(entry, navOptions)

        if (!initialNavigation) {
            val outgoingEntry = state.backStack.value.lastOrNull()
            // if outgoing entry is initial entry, FragmentManager still triggers onBackStackChange
            // callback for it, so we don't filter out initial entry here
            if (outgoingEntry != null) {
                addPendingOps(outgoingEntry.id)
            }
            // add pending ops here before any animation (if present) starts
            addPendingOps(entry.id)
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("navigate 添加导航操作: ${formatEntryInfo(entry)}, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
            }
            ft.addToBackStack(entry.id)
        }

        if (navigatorExtras is Extras) {
            for ((key, value) in navigatorExtras.sharedElements) {
                ft.addSharedElement(key, value)
            }
        }
        ft.commit()
        // The commit succeeded, update our view of the world
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("navigate 执行入栈转换: ${formatEntryInfo(entry)}, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
        }
        state.pushWithTransition(entry)
    }

    /**
     * {@inheritDoc}
     *
     * This method should always call [FragmentTransaction.setPrimaryNavigationFragment] so that the
     * Fragment associated with the new destination can be retrieved with
     * [FragmentManager.getPrimaryNavigationFragment].
     *
     * Note that the default implementation commits the new Fragment asynchronously, so the new
     * Fragment is not instantly available after this call completes.
     *
     * This call will be ignored if the FragmentManager state has already been saved.
     */
    override fun onLaunchSingleTop(backStackEntry: NavBackStackEntry) {
        if (fragmentManager.isStateSaved) {
            Timber.tag(TAG).i("onLaunchSingleTop 忽略调用: FragmentManager已保存状态")
            return
        }
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("onLaunchSingleTop 启动单顶模式: ${formatEntryInfo(backStackEntry)}, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
        }
        val ft = createFragmentTransaction(backStackEntry, null)
        val backstack = state.backStack.value
        if (backstack.size > 1) {
            // If the Fragment to be replaced is on the FragmentManager's
            // back stack, a simple replace() isn't enough so we
            // remove it from the back stack and put our replacement
            // on the back stack in its place
            val incomingEntry = backstack.elementAtOrNull(backstack.lastIndex - 1)
            if (incomingEntry != null) {
                addPendingOps(incomingEntry.id)
            }
            addPendingOps(backStackEntry.id, isPop = true)
            fragmentManager.popBackStack(
                backStackEntry.id, FragmentManager.POP_BACK_STACK_INCLUSIVE
            )

            addPendingOps(backStackEntry.id, deduplicate = false)
            ft.addToBackStack(backStackEntry.id)
        }
        ft.commit()
        // The commit succeeded, update our view of the world
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("onLaunchSingleTop 执行单顶转换: ${formatEntryInfo(backStackEntry)}")
        }
        state.onLaunchSingleTop(backStackEntry)
    }

    private fun createFragmentTransaction(
        entry: NavBackStackEntry, navOptions: NavOptions?
    ): FragmentTransaction {
        val destination = entry.destination as Destination
        val args = entry.arguments
        var className = destination.className
        if (className[0] == '.') {
            className = context.packageName + className
        }
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("createFragmentTransaction 创建Fragment事务: entry=[${formatEntryInfo(entry)}], navOptions=$navOptions,  state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")
        }
        // 保留Fragment状态的逻辑
        val ft = fragmentManager.beginTransaction()
        var enterAnim = navOptions?.enterAnim ?: -1
        var exitAnim = navOptions?.exitAnim ?: -1
        var popEnterAnim = navOptions?.popEnterAnim ?: -1
        var popExitAnim = navOptions?.popExitAnim ?: -1
        if (enterAnim != -1 || exitAnim != -1 || popEnterAnim != -1 || popExitAnim != -1) {
            enterAnim = if (enterAnim != -1) enterAnim else 0
            exitAnim = if (exitAnim != -1) exitAnim else 0
            popEnterAnim = if (popEnterAnim != -1) popEnterAnim else 0
            popExitAnim = if (popExitAnim != -1) popExitAnim else 0
            ft.setCustomAnimations(enterAnim, exitAnim, popEnterAnim, popExitAnim)
        }

        var frag: Fragment? = fragmentManager.primaryNavigationFragment //查找当前导航栈顶的fragment

        // 如果当前页面上面，有盖DialogFragment，就自动关闭掉
        frag?.childFragmentManager?.fragments?.forEach {
            if (it is DialogFragment) {

//                val beginTransaction = frag.childFragmentManager.beginTransaction()
//                beginTransaction.hide(it)

                it.dismissAllowingStateLoss()

//                it.dialog?.hide()
                Timber.tag(TAG).d("createFragmentTransaction 关闭子DialogFragment: ${it.javaClass.name}")
                // TODO 在back的时候要不要展示回来, 待处理。目前是不在返回的时候不再展示回来了
            }
        }

        fragmentManager.fragments.forEach {
            if (it is DialogFragment) {
//                it.dialog?.hide()
                it.dismissAllowingStateLoss()
                Timber.tag(TAG).d("createFragmentTransaction 关闭子DialogFragment: ${it.javaClass.name}")
                // TODO 在back的时候要不要展示回来, 待处理。目前是不在返回的时候不再展示回来了
            }
        }

        //判断是否需要重新创建一个新的Fragment
        var needRecreate = false
        //提前判断。如果当栈顶Fragment 等于 目的地Fragment。在逻辑上属于自己打开自己。
        //所以当逻辑是这样的，就需要重新创建一个自己。
        if (frag?.javaClass?.name == className) {
            needRecreate = true
        }

        //如果栈顶存在Fragment，就hide。
        if (frag != null) {
            ft.setMaxLifecycle(frag, Lifecycle.State.STARTED)
            ft.hide(frag)
        }

        // 修复：使用entry.id作为tag，确保与Navigation状态一致
        val tag = entry.id
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("createFragmentTransaction 查找Fragment: entry.id=$tag, destination.id=${destination.id}")
        }
        frag = fragmentManager.findFragmentByTag(tag)

        // 如果该次跳转是inclusive的，那么就需要重新创建一个新的Fragment，不然多次跳转会白屏
        if (frag != null && navOptions?.isPopUpToInclusive() == true) {
            needRecreate = true
        }

        //这里判断是否需要重建，如果需要重建就不show。而是重新创建一个。
        if (frag != null && !needRecreate) {
            //fragment 已经存在并且不用重建则显示
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("createFragmentTransaction 重用现有Fragment: ${getFragmentSimpleName(frag)}, tag=${frag.tag}, 期望tag=$tag")
            }
            ft.setMaxLifecycle(frag, Lifecycle.State.RESUMED)
            ft.show(frag)
        } else {
            // 如果需要重建且fragment存在，先移除旧的fragment
            if (frag != null && needRecreate) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("createFragmentTransaction 移除旧Fragment进行重建: ${getFragmentSimpleName(frag)}, 旧tag=${frag.tag}, 新tag=$tag")
                }
                ft.remove(frag)
            }
            //fragment 不存在创建，添加
            frag = fragmentManager.fragmentFactory.instantiate(context.classLoader, className)
            frag.arguments = args//设置参数.
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("createFragmentTransaction 创建新Fragment: ${getFragmentSimpleName(frag)}, 设置tag=$tag")
            }
            ft.add(containerId, frag, tag)
        }
        ft.setPrimaryNavigationFragment(frag)
        ft.setReorderingAllowed(true)
        return ft
    }

    public override fun onSaveState(): Bundle? {
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("onSaveState 保存状态: savedIds=${savedIds}")
        }
        if (savedIds.isEmpty()) {
            return null
        }
        return bundleOf(KEY_SAVED_IDS to ArrayList(savedIds))
    }

    public override fun onRestoreState(savedState: Bundle) {
        if (isLoggingEnabled(Log.VERBOSE)) {
            Timber.tag(TAG).d("onRestoreState 恢复状态: savedIds=${savedIds}")
        }
        val savedIds = savedState.getStringArrayList(KEY_SAVED_IDS)
        if (savedIds != null) {
            this.savedIds.clear()
            this.savedIds += savedIds
        }
    }

    /**
     * NavDestination specific to [FragmentNavigator]
     *
     * Construct a new fragment destination. This destination is not valid until you set the
     * Fragment via [setClassName].
     *
     * @param fragmentNavigator The [FragmentNavigator] which this destination will be associated
     *   with. Generally retrieved via a [NavController]'s [NavigatorProvider.getNavigator] method.
     */
    @NavDestination.ClassType(Fragment::class)
    public open class Destination
    public constructor(fragmentNavigator: Navigator<out Destination>) : NavDestination(fragmentNavigator) {

        /**
         * Construct a new fragment destination. This destination is not valid until you set the
         * Fragment via [setClassName].
         *
         * @param navigatorProvider The [NavController] which this destination will be associated
         *   with.
         */
        public constructor(
            navigatorProvider: NavigatorProvider
        ) : this(navigatorProvider.getNavigator(FragmentNavigatorPlus::class.java))

        @CallSuper
        public override fun onInflate(context: Context, attrs: AttributeSet) {
            super.onInflate(context, attrs)
            context.resources.obtainAttributes(attrs, R.styleable.FragmentNavigator).use { array ->
                val className = array.getString(R.styleable.FragmentNavigator_android_name)
                if (className != null) setClassName(className)
            }
        }

        /**
         * Set the Fragment class name associated with this destination
         *
         * @param className The class name of the Fragment to show when you navigate to this
         *   destination
         * @return this [Destination]
         */
        public fun setClassName(className: String): Destination {
            _className = className
            return this
        }

        private var _className: String? = null

        /**
         * The Fragment's class name associated with this destination
         *
         * @throws IllegalStateException when no Fragment class was set.
         */
        public val className: String
            get() {
                checkNotNull(_className) { "Fragment class was not set" }
                return _className as String
            }

        public override fun toString(): String {
            val sb = StringBuilder()
            sb.append(super.toString())
            sb.append(" class=")
            if (_className == null) {
                sb.append("null")
            } else {
                sb.append(_className)
            }
            return sb.toString()
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other == null || other !is Destination) return false
            return super.equals(other) && _className == other._className
        }

        override fun hashCode(): Int {
            var result = super.hashCode()
            result = 31 * result + _className.hashCode()
            return result
        }
    }

    /** Extras that can be passed to FragmentNavigator to enable Fragment specific behavior */
    public class Extras internal constructor(sharedElements: Map<View, String>) : Navigator.Extras {
        private val _sharedElements = LinkedHashMap<View, String>()

        /**
         * The map of shared elements associated with these Extras. The returned map is an
         * [unmodifiable][Map] copy of the underlying map and should be treated as immutable.
         */
        public val sharedElements: Map<View, String>
            get() = _sharedElements.toMap()

        /**
         * Builder for constructing new [Extras] instances. The resulting instances are immutable.
         */
        public class Builder {
            private val _sharedElements = LinkedHashMap<View, String>()

            /**
             * Adds multiple shared elements for mapping Views in the current Fragment to
             * transitionNames in the Fragment being navigated to.
             *
             * @param sharedElements Shared element pairs to add
             * @return this [Builder]
             */
            public fun addSharedElements(sharedElements: Map<View, String>): Builder {
                for ((view, name) in sharedElements) {
                    addSharedElement(view, name)
                }
                return this
            }

            /**
             * Maps the given View in the current Fragment to the given transition name in the
             * Fragment being navigated to.
             *
             * @param sharedElement A View in the current Fragment to match with a View in the
             *   Fragment being navigated to.
             * @param name The transitionName of the View in the Fragment being navigated to that
             *   should be matched to the shared element.
             * @return this [Builder]
             * @see FragmentTransaction.addSharedElement
             */
            public fun addSharedElement(sharedElement: View, name: String): Builder {
                _sharedElements[sharedElement] = name
                return this
            }

            /**
             * Constructs the final [Extras] instance.
             *
             * @return An immutable [Extras] instance.
             */
            public fun build(): Extras {
                return Extras(_sharedElements)
            }
        }

        init {
            _sharedElements.putAll(sharedElements)
        }
    }

    private companion object {
        private const val TAG = "FragmentNavigatorPlus"
        private const val KEY_SAVED_IDS = "androidx-nav-fragment:navigator:savedIds"
    }

    internal class ClearEntryStateViewModel : ViewModel() {
        lateinit var completeTransition: WeakReference<() -> Unit>

        override fun onCleared() {
            super.onCleared()
            completeTransition.get()?.invoke()
        }
    }

    /**
     * In general, each entry would only get one callback within a transaction except for single top
     * transactions, where we would get two callbacks for the same entry.
     */
    private fun addPendingOps(id: String, isPop: Boolean = false, deduplicate: Boolean = true) {
        if (deduplicate) {
            pendingOps.removeAll { it.first == id }
        }
        pendingOps.add(id to isPop)

        if (isLoggingEnabled(Log.VERBOSE)) {
            val entry = (state.backStack.value + state.transitionsInProgress.value).find { it.id == id }
            val fragmentName = entry?.let { getFragmentSimpleName((it.destination as Destination).className) } ?: "unknown"
            val simplifiedId = getSimplifiedEntryId(id)
            val popSuffix = if (isPop) "[pop]" else ""

            Timber.tag(TAG).d("addPendingOps 添加待处理操作: $fragmentName($simplifiedId)$popSuffix, 总数=${pendingOps.size}, state栈=[${formatEntryListInfo(state.backStack.value)}], FM活跃栈=[${getFragmentManagerStackInfo()}], FM回退栈=[${getFragmentManagerBackStackInfo()}]")

            // 安全检查：如果pendingOps过多，可能是状态不同步
            if (pendingOps.size > 10) {
                Timber.tag(TAG).w("addPendingOps 待处理操作过多(${pendingOps.size}), 可能状态不同步: [${formatPendingOpsInfo()}]")
            }
        }
    }

    /**
     * 清理过期的pending operations
     * 移除那些对应的Fragment已经不在FragmentManager中的操作
     */
    private fun cleanupStalePendingOps() {
        val activeFragmentTags = fragmentManager.fragments.mapNotNull { it.tag }.toSet()
        val backStackTags = mutableSetOf<String>()

        // 收集FragmentManager回退栈中的所有tag
        for (i in 0 until fragmentManager.backStackEntryCount) {
            try {
                val entry = fragmentManager.getBackStackEntryAt(i)
                entry.name?.let { backStackTags.add(it) }
            } catch (e: Exception) {
                // 忽略异常
            }
        }

        val allFragmentTags = activeFragmentTags + backStackTags
        val initialSize = pendingOps.size

        // 移除那些Fragment已经不存在的pending operations
        pendingOps.removeAll { (id, _) ->
            !allFragmentTags.contains(id) &&
            !(state.backStack.value + state.transitionsInProgress.value).any { it.id == id }
        }

        if (isLoggingEnabled(Log.VERBOSE) && pendingOps.size != initialSize) {
            Timber.tag(TAG).d("cleanupStalePendingOps 清理过期操作: 清理前=${initialSize}, 清理后=${pendingOps.size}")
        }
    }

    /**
     * 验证并尝试修复Navigation状态与FragmentManager状态的不一致
     */
    private fun validateAndSyncState(operation: String) {
        try {
            val navStackIds = state.backStack.value.map { it.id }
            val navTransitionsIds = state.transitionsInProgress.value.map { it.id }
            val allNavIds = navStackIds + navTransitionsIds

            val activeFragments = fragmentManager.fragments
            val orphanFragments = activeFragments.filter { fragment ->
                val tag = fragment.tag
                tag != null && !allNavIds.contains(tag) && !fragment.isRemoving
            }

            // 检查Navigation栈与FragmentManager回退栈的一致性
            val fmBackStackIds = mutableListOf<String>()
            for (i in 0 until fragmentManager.backStackEntryCount) {
                try {
                    val entry = fragmentManager.getBackStackEntryAt(i)
                    entry.name?.let { fmBackStackIds.add(it) }
                } catch (e: Exception) {
                    // 忽略异常
                }
            }

            // 检查是否存在Navigation栈比FragmentManager栈多的情况
            val navOnlyIds = navStackIds.filter { !fmBackStackIds.contains(it) }
            if (navOnlyIds.isNotEmpty()) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).w("validateAndSyncState [$operation] 检测到Navigation栈包含FragmentManager中不存在的entries: ${navOnlyIds.map { getSimplifiedEntryId(it) }}")
                    Timber.tag(TAG).w("validateAndSyncState [$operation] Navigation栈: ${navStackIds.map { getSimplifiedEntryId(it) }}")
                    Timber.tag(TAG).w("validateAndSyncState [$operation] FragmentManager栈: ${fmBackStackIds.map { getSimplifiedEntryId(it) }}")
                }

                // 尝试修复Navigation栈中多余的entries
                tryFixNavigationStackInconsistency(navOnlyIds, operation)
            }

            // 如果发现孤儿Fragment且pending operations过多，尝试清理
            if (orphanFragments.isNotEmpty() && pendingOps.size > 15) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).w("validateAndSyncState [$operation] 检测到严重状态不同步，尝试清理: 孤儿Fragment=${orphanFragments.size}, pendingOps=${pendingOps.size}")
                }

                // 强制清理所有pending operations
                pendingOps.clear()

                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("validateAndSyncState [$operation] 强制清理完成: pendingOps=${pendingOps.size}")
                }
            }

            // 检查并修复孤儿Fragment
            if (orphanFragments.isNotEmpty()) {
                tryFixOrphanFragments(orphanFragments, operation)
            }

        } catch (e: Exception) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).e("validateAndSyncState [$operation] 状态验证失败: ${e.message}")
            }
        }
    }

    /**
     * 尝试修复Navigation栈中多余的entries
     */
    private fun tryFixNavigationStackInconsistency(navOnlyIds: List<String>, operation: String) {
        try {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("tryFixNavigationStackInconsistency [$operation] 检查Navigation栈不一致，但暂时不执行自动修复")
                Timber.tag(TAG).d("tryFixNavigationStackInconsistency [$operation] Navigation独有entries: ${navOnlyIds.map { getSimplifiedEntryId(it) }}")
            }

            // 暂时禁用自动修复逻辑，因为它可能导致正常的Fragment被错误销毁
            // 只记录状态不一致，不执行实际的修复操作

            navOnlyIds.forEach { entryId ->
                val entry = state.backStack.value.find { it.id == entryId }
                if (entry != null) {
                    val destination = entry.destination as? Destination
                    val fragmentClassName = destination?.className

                    if (isLoggingEnabled(Log.VERBOSE)) {
                        Timber.tag(TAG).d("tryFixNavigationStackInconsistency [$operation] Navigation独有entry: ${formatEntryInfo(entry)}, className=$fragmentClassName")
                    }

                    // 只有在非常特殊的情况下才执行修复
                    // 例如：确实是已经不存在的Fragment，且不是正常导航流程的一部分
                    val shouldFix = false // 暂时禁用所有自动修复

                    if (shouldFix && fragmentClassName?.contains("SplashFragment") == true) {
                        // 这里可以添加更严格的检查条件
                        if (isLoggingEnabled(Log.VERBOSE)) {
                            Timber.tag(TAG).w("tryFixNavigationStackInconsistency [$operation] 发现可能需要清理的entry: ${formatEntryInfo(entry)}")
                        }
                    }
                }
            }

        } catch (e: Exception) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).e("tryFixNavigationStackInconsistency [$operation] 检查失败: ${e.message}")
            }
        }
    }

    /**
     * 尝试修复孤儿Fragment
     */
    private fun tryFixOrphanFragments(orphanFragments: List<Fragment>, operation: String) {
        try {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).d("tryFixOrphanFragments [$operation] 发现${orphanFragments.size}个孤儿Fragment，但暂时不执行自动修复")
            }

            orphanFragments.forEach { fragment ->
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).d("tryFixOrphanFragments [$operation] 孤儿Fragment: ${getFragmentSimpleName(fragment)}(${fragment.tag})")

                    // 提供更多调试信息
                    val isPrimaryNavigation = fragmentManager.primaryNavigationFragment == fragment
                    val isVisible = fragment.isVisible
                    val isAdded = fragment.isAdded
                    val isRemoving = fragment.isRemoving
                    val lifecycle = fragment.lifecycle.currentState

                    Timber.tag(TAG).d("tryFixOrphanFragments [$operation] Fragment状态: isPrimary=$isPrimaryNavigation, isVisible=$isVisible, isAdded=$isAdded, isRemoving=$isRemoving, lifecycle=$lifecycle")
                }

                // 暂时禁用所有自动修复逻辑，只记录状态
                // 这样可以避免意外删除正在使用的Fragment

                // 只有在非常特殊的情况下才考虑修复
                // 例如：Fragment确实已经不应该存在，且不会影响用户体验
                val shouldFix = false // 暂时禁用所有自动修复

                if (shouldFix && fragment.isAdded && !fragment.isRemoving) {
                    try {
                        // 检查Fragment是否是当前显示的Fragment
                        val isPrimaryNavigation = fragmentManager.primaryNavigationFragment == fragment
                        if (!isPrimaryNavigation) {
                            if (isLoggingEnabled(Log.VERBOSE)) {
                                Timber.tag(TAG).w("tryFixOrphanFragments [$operation] 可能需要移除的非主导航孤儿Fragment: ${getFragmentSimpleName(fragment)}")
                            }
                            // 这里可以添加更严格的检查和修复逻辑
                        }
                    } catch (e: Exception) {
                        if (isLoggingEnabled(Log.VERBOSE)) {
                            Timber.tag(TAG).e("tryFixOrphanFragments [$operation] 检查孤儿Fragment失败: ${e.message}")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).e("tryFixOrphanFragments [$operation] 检查孤儿Fragment失败: ${e.message}")
            }
        }
    }

    /**
     * 检查并强制进行状态恢复
     * 当检测到严重的状态不一致时，尝试强制恢复到正确状态
     */
    private fun checkAndForceStateRecovery(operation: String) {
        try {
            val navStackIds = state.backStack.value.map { it.id }
            val fmBackStackIds = mutableListOf<String>()

            // 获取FragmentManager回退栈
            for (i in 0 until fragmentManager.backStackEntryCount) {
                try {
                    val entry = fragmentManager.getBackStackEntryAt(i)
                    entry.name?.let { fmBackStackIds.add(it) }
                } catch (e: Exception) {
                    // 忽略异常
                }
            }

            // 检查是否存在严重的状态不一致
            val navOnlyIds = navStackIds.filter { !fmBackStackIds.contains(it) }
            val fmOnlyIds = fmBackStackIds.filter { !navStackIds.contains(it) }

            // 如果Navigation栈和FragmentManager栈完全不匹配，且存在孤儿Fragment
            val activeFragments = fragmentManager.fragments
            val orphanFragments = activeFragments.filter { fragment ->
                val tag = fragment.tag
                tag != null && !navStackIds.contains(tag) && !fragment.isRemoving
            }

            val needForceRecovery = (navOnlyIds.size >= 2 || fmOnlyIds.size >= 2) && orphanFragments.isNotEmpty()

            if (needForceRecovery) {
                if (isLoggingEnabled(Log.VERBOSE)) {
                    Timber.tag(TAG).w("checkAndForceStateRecovery [$operation] 检测到严重状态不一致，尝试强制恢复")
                    Timber.tag(TAG).w("checkAndForceStateRecovery [$operation] Navigation独有: ${navOnlyIds.map { getSimplifiedEntryId(it) }}")
                    Timber.tag(TAG).w("checkAndForceStateRecovery [$operation] FragmentManager独有: ${fmOnlyIds.map { getSimplifiedEntryId(it) }}")
                    Timber.tag(TAG).w("checkAndForceStateRecovery [$operation] 孤儿Fragment: ${orphanFragments.map { "${getFragmentSimpleName(it)}(${getSimplifiedEntryId(it.tag ?: "")})" }}")
                }

                // 强制清理所有pending operations
                pendingOps.clear()

                // 尝试同步状态：以FragmentManager的状态为准
                forceStateRecoveryBasedOnFragmentManager(operation)
            }
        } catch (e: Exception) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).e("checkAndForceStateRecovery [$operation] 检查失败: ${e.message}")
            }
        }
    }

    /**
     * 基于FragmentManager状态强制恢复Navigation状态
     */
    private fun forceStateRecoveryBasedOnFragmentManager(operation: String) {
        try {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).w("forceStateRecoveryBasedOnFragmentManager [$operation] 开始强制状态恢复")
            }

            // 获取当前主导航Fragment
            val primaryFragment = fragmentManager.primaryNavigationFragment
            if (primaryFragment != null) {
                val primaryFragmentTag = primaryFragment.tag
                if (primaryFragmentTag != null) {
                    // 检查这个Fragment是否在Navigation栈中
                    val correspondingEntry = state.backStack.value.find { it.id == primaryFragmentTag }

                    if (correspondingEntry == null) {
                        if (isLoggingEnabled(Log.VERBOSE)) {
                            Timber.tag(TAG).w("forceStateRecoveryBasedOnFragmentManager [$operation] 主导航Fragment不在Navigation栈中: ${getFragmentSimpleName(primaryFragment)}($primaryFragmentTag)")
                        }

                        // 这种情况下，我们需要重新同步状态
                        // 但由于Navigation的限制，我们不能直接修改backStack
                        // 只能通过标记转换完成来让系统自动清理不一致的状态
                        state.transitionsInProgress.value.forEach { entry ->
                            if (isLoggingEnabled(Log.VERBOSE)) {
                                Timber.tag(TAG).d("forceStateRecoveryBasedOnFragmentManager [$operation] 标记转换完成: ${formatEntryInfo(entry)}")
                            }
                            state.markTransitionComplete(entry)
                        }
                    }
                }
            }

            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).w("forceStateRecoveryBasedOnFragmentManager [$operation] 强制状态恢复完成")
            }
        } catch (e: Exception) {
            if (isLoggingEnabled(Log.VERBOSE)) {
                Timber.tag(TAG).e("forceStateRecoveryBasedOnFragmentManager [$operation] 强制恢复失败: ${e.message}")
            }
        }
    }


}
