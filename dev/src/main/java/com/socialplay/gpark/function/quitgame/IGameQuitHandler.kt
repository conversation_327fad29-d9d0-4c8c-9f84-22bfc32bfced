package com.socialplay.gpark.function.quitgame

import android.app.Activity
import androidx.fragment.app.DialogFragment
import com.socialplay.gpark.data.model.game.GameQuitInfo

interface IGameQuitHandler {

    /**
     * 默认调度器Dispatchers.IO
     * 有需要请切Dispatchers.Main
     * @return 是否拦截 isIntercept
     */
    suspend fun onGameQuit(
        activity: Activity,
        dialogFragment: DialogFragment?,
        gameInfo: GameQuitInfo,
        quitReason: Int
    ): Boolean

}