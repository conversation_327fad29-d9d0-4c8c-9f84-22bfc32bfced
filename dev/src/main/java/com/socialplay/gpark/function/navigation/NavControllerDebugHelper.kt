package com.socialplay.gpark.function.navigation

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import timber.log.Timber

/**
 * 扩展函数：查找指定类型的Fragment
 */
inline fun <reified T> List<Fragment>.findTyped(): T? {
    return this.find { it is T } as? T
}

/**
 * NavController调试工具类
 * 用于打印NavController的内部状态信息，包括backQueue等
 *
 * 使用示例：
 *
 * // 在Fragment中快速调试（推荐）
 * NavControllerDebugHelper.debugCurrentState(this, "onResume")
 *
 * // 在Fragment中打印NavController状态
 * NavControllerDebugHelper.printNavControllerStateFromFragment(this, "onResume")
 *
 * // 在Activity中打印NavController状态
 * val navHostFragment = supportFragmentManager.fragments.findTyped<NavHostFragment>()
 * navHostFragment?.let {
 *     NavControllerDebugHelper.printNavControllerStateFromFragmentManager(
 *         it.childFragmentManager,
 *         this,
 *         "onCreate"
 *     )
 * }
 *
 * // 打印完整的导航状态信息
 * NavControllerDebugHelper.printFullNavigationState(
 *     fragmentManager,
 *     context,
 *     "debug",
 *     "额外信息"
 * )
 */
object NavControllerDebugHelper {

    private const val TAG = "NavControllerDebugHelpe"

        /**
     * 获取NavController的backQueue信息（通过反射）
     */
    fun getNavControllerBackQueueInfo(navController: NavController, context: Context): String {
        return try {
            val controllerClass = navController.javaClass
            Timber.tag(TAG).d("NavController实际类型: ${controllerClass.name}")
            
            // 列出所有字段，帮助我们找到正确的字段名
            Timber.tag(TAG).d("NavController所有字段:")
            controllerClass.declaredFields.forEach { field ->
                Timber.tag(TAG).d("  字段: ${field.name}, 类型: ${field.type.simpleName}")
            }
            
            // 尝试从父类获取字段
            var backQueueField: java.lang.reflect.Field? = null
            var currentClass: Class<*>? = controllerClass
            
            while (currentClass != null && backQueueField == null) {
                try {
                    backQueueField = currentClass.getDeclaredField("backQueue")
                    Timber.tag(TAG).d("在类 ${currentClass.simpleName} 中找到backQueue字段")
                } catch (e: NoSuchFieldException) {
                    Timber.tag(TAG).d("在类 ${currentClass.simpleName} 中未找到backQueue字段")
                    currentClass = currentClass.superclass
                }
            }
            
            if (backQueueField == null) {
                return "error: 在所有父类中都未找到backQueue字段"
            }
            
            backQueueField.isAccessible = true
            val backQueue = backQueueField.get(navController)
            
            Timber.tag(TAG).d("backQueue字段类型: ${backQueue?.javaClass?.name}")

            if (backQueue is ArrayDeque<*>) {
                // 使用ArrayDeque的公共方法获取内容
                val entries = mutableListOf<String>()
                val iterator = backQueue.iterator()
                var index = 0
                
                while (iterator.hasNext() && index < 10) { // 限制最多10个条目避免无限循环
                    val entry = iterator.next()
                    try {
                        Timber.tag(TAG).d("处理entry[$index]: ${entry?.javaClass?.simpleName}")
                        
                        // 获取entry的id
                        var entryId = "null"
                        try {
                            val idField = entry?.javaClass?.getDeclaredField("id")
                            idField?.isAccessible = true
                            entryId = idField?.get(entry) as? String ?: "null"
                        } catch (e: Exception) {
                            Timber.tag(TAG).d("获取entry id失败: ${e.message}")
                        }
                        
                        // 获取entry的destination
                        var destinationName = "unknown"
                        // 尝试不同的方式获取destination的label
                        var destinationLabel = "unknown"
                        try {
                            val destinationField = entry?.javaClass?.getDeclaredField("destination")
                            destinationField?.isAccessible = true
                            val destination = destinationField?.get(entry)

                            if (destination != null) {
                                val destinationClass = destination.javaClass
                                Timber.tag(TAG).d("destination类型: ${destinationClass.simpleName}")

                                // 尝试不同的方式获取destination的id
                                var destinationId = -1
                                try {
                                    val destIdField = destinationClass.getDeclaredField("id")
                                    destIdField.isAccessible = true
                                    destinationId = destIdField.get(destination) as? Int ?: -1
                                } catch (e: Exception) {
//                                    Timber.tag(TAG).d("获取destination id失败: ${e.message}")
                                }

                                // 尝试获取Fragment类名
                                try {
                                    // 对于FragmentNavigatorPlus.Destination，尝试获取className字段
                                    if (destinationClass.simpleName == "Destination") {
                                        val classNameField = destinationClass.getDeclaredField("_className")
                                        classNameField.isAccessible = true
                                        val className = classNameField.get(destination) as? String
                                        if (className != null) {
                                            destinationLabel = className.substringAfterLast(".")
                                        }
                                    } else {
                                        // 对于其他类型的Destination，尝试获取label字段
                                        val destLabelField = destinationClass.getDeclaredField("label")
                                        destLabelField.isAccessible = true
                                        destinationLabel = destLabelField.get(destination) as? String ?: "unknown"
                                    }
                                } catch (e: Exception) {
                                    Timber.tag(TAG).d("获取destination label失败: ${e.message}")
                                }

                                if (destinationId != -1) {
                                    try {
                                        val resourceName = context.resources.getResourceEntryName(destinationId)
                                        destinationName = resourceName.substringAfterLast("_")
                                    } catch (e: Exception) {
                                        destinationName = "id$destinationId"
                                    }
                                } else {
                                    // 如果是自定义Destination，尝试获取className
                                    try {
                                        val classNameField = destinationClass.getDeclaredField("className")
                                        classNameField.isAccessible = true
                                        val className = classNameField.get(destination) as? String
                                        if (className != null) {
                                            destinationName = className.substringAfterLast(".")
                                        }
                                    } catch (e: Exception) {
                                        destinationName = destinationClass.simpleName
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Timber.tag(TAG).d("获取destination失败: ${e.message}")
                        }
                        
                        val simplifiedId = if (entryId.length > 8) entryId.substring(0, 8) else entryId
                        entries.add("$destinationName.$destinationLabel($simplifiedId)")
                        index++
                    } catch (e: Exception) {
                        entries.add("entry$index(error: ${e.message})")
                        index++
                    }
                }
                
                entries.joinToString(", ")
            } else {
                "not_ArrayDeque: ${backQueue?.javaClass?.simpleName}"
            }
        } catch (e: Exception) {
            "error: ${e.message}"
        }
    }

        /**
     * 打印NavController的完整状态信息
     */
    fun printNavControllerState(navController: NavController, context: Context, operation: String) {
        val backQueueInfo = getNavControllerBackQueueInfo(navController, context)
        val currentDestination = navController.currentDestination?.let { dest ->
            try {
                val resourceName = context.resources.getResourceEntryName(dest.id)
                resourceName.substringAfterLast("_")
            } catch (e: Exception) {
                "id${dest.id}"
            }
        } ?: "null"
        
                // 获取回退栈信息（使用公共API）
        val backStackEntries = try {
            val previousBackStackEntry = navController.previousBackStackEntry
            val currentBackStackEntry = navController.currentBackStackEntry
            
            val entries = mutableListOf<String>()
            if (previousBackStackEntry != null) {
                val destination = previousBackStackEntry.destination
                val destinationName = try {
                    val resourceName = context.resources.getResourceEntryName(destination.id)
                    resourceName.substringAfterLast("_")
                } catch (e: Exception) {
                    "id${destination.id}"
                }
                val simplifiedId = if (previousBackStackEntry.id.length > 8) previousBackStackEntry.id.substring(0, 8) else previousBackStackEntry.id
                entries.add("$destinationName($simplifiedId)")
            }
            if (currentBackStackEntry != null) {
                val destination = currentBackStackEntry.destination
                val destinationName = try {
                    val resourceName = context.resources.getResourceEntryName(destination.id)
                    resourceName.substringAfterLast("_")
                } catch (e: Exception) {
                    "id${destination.id}"
                }
                val simplifiedId = if (currentBackStackEntry.id.length > 8) currentBackStackEntry.id.substring(0, 8) else currentBackStackEntry.id
                entries.add("$destinationName($simplifiedId)")
            }
            entries
        } catch (e: Exception) {
            listOf("error: ${e.message}")
        }
        
        Timber.tag(TAG).d("NavController状态 [$operation]:")
        Timber.tag(TAG).d("  currentDestination: $currentDestination")
        Timber.tag(TAG).d("  backStackEntries: [${backStackEntries.joinToString(", ")}]")
        Timber.tag(TAG).d("  backQueue(reflection): [$backQueueInfo]")
        Timber.tag("FragmentNavigatorPlus").d("printBackQueue(reflection): [$backQueueInfo]")
        Timber.tag(TAG).d("  previousBackStackEntry: ${navController.previousBackStackEntry?.destination?.let { try { context.resources.getResourceEntryName(it.id).substringAfterLast("_") } catch (e: Exception) { "id${it.id}" } } ?: "null"}")
        Timber.tag(TAG).d("  currentBackStackEntry: ${navController.currentBackStackEntry?.destination?.let { try { context.resources.getResourceEntryName(it.id).substringAfterLast("_") } catch (e: Exception) { "id${it.id}" } } ?: "null"}")
    }

    /**
     * 从Fragment获取NavController并打印状态
     */
    fun printNavControllerStateFromFragment(fragment: Fragment, operation: String) {
        try {
            val navController = fragment.findNavController()
            printNavControllerState(navController, fragment.requireContext(), operation)
        } catch (e: Exception) {
            Timber.tag(TAG).w("从Fragment获取NavController失败 [$operation]: ${e.message}")
        }
    }

    /**
     * 从FragmentManager获取NavController并打印状态
     */
    fun printNavControllerStateFromFragmentManager(fragmentManager: FragmentManager, context: Context, operation: String) {
        try {
            Timber.tag(TAG).d("=== 从FragmentManager获取NavController [$operation] ===")

            // 打印FragmentManager中的fragments信息
            val fragments = fragmentManager.fragments
            Timber.tag(TAG).d("FragmentManager中的fragments数量: ${fragments.size}")
            fragments.forEachIndexed { index, fragment ->
                Timber.tag(TAG).d("  Fragment[$index]: ${fragment.javaClass.simpleName}, tag=${fragment.tag}, isAdded=${fragment.isAdded}")
                if (fragment is NavHostFragment) {
                    Timber.tag(TAG).d("    *** 这是NavHostFragment ***")
                }
            }

            // 方法1: 从fragments列表中查找NavHostFragment
            var navController: NavController? = null
            var navControllerSource = "unknown"

            val navHostFragment = fragments.firstOrNull { it is NavHostFragment } as? NavHostFragment
            if (navHostFragment != null) {
                navController = navHostFragment.navController
                navControllerSource = "fragments.firstOrNull"
                Timber.tag(TAG).d("方法1成功: 从fragments列表中找到NavHostFragment")
            } else {
                Timber.tag(TAG).w("方法1失败: 在fragments列表中未找到NavHostFragment")
            }

            // 方法2: 尝试从Activity的FragmentManager获取
            // 这里需要从context获取Activity
            val activity = context as? FragmentActivity
            if (navController == null) {
                try {
                    if (activity != null) {
                        val activityNavHostFragment = activity.supportFragmentManager.fragments.findTyped<NavHostFragment>()
                        if (activityNavHostFragment != null) {
                            navController = activityNavHostFragment.navController
                            navControllerSource = "activity.supportFragmentManager"
                            Timber.tag(TAG).d("方法2成功: 从Activity的FragmentManager中找到NavHostFragment")
                        } else {
                            Timber.tag(TAG).w("方法2失败: 在Activity的FragmentManager中未找到NavHostFragment")
                        }
                    } else {
                        Timber.tag(TAG).w("方法2跳过: context不是Activity")
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).w("方法2异常: ${e.message}")
                }
            }

//            // 方法3: 尝试从FragmentManager的parent获取
//            if (navController == null) {
//                try {
//                    val parentFragmentManager = activity?.supportFragmentManager.s
//                    if (parentFragmentManager != fragmentManager) {
//                        val parentNavHostFragment = parentFragmentManager.fragments.findTyped<NavHostFragment>()
//                        if (parentNavHostFragment != null) {
//                            navController = parentNavHostFragment.navController
//                            navControllerSource = "parentFragmentManager"
//                            Timber.tag(TAG).d("方法3成功: 从parentFragmentManager中找到NavHostFragment")
//                        } else {
//                            Timber.tag(TAG).w("方法3失败: 在parentFragmentManager中未找到NavHostFragment")
//                        }
//                    } else {
//                        Timber.tag(TAG).w("方法3跳过: 没有parentFragmentManager")
//                    }
//                } catch (e: Exception) {
//                    Timber.tag(TAG).w("方法3异常: ${e.message}")
//                }
//            }

            // 打印NavController状态
            if (navController != null) {
                Timber.tag(TAG).d("NavController获取成功，来源: $navControllerSource")
                printNavControllerState(navController, context, operation)
            } else {
                Timber.tag(TAG).e("所有方法都无法获取NavController")
            }

            Timber.tag(TAG).d("=== 从FragmentManager获取NavController结束 [$operation] ===")
        } catch (e: Exception) {
            Timber.tag(TAG).e("从FragmentManager获取NavController失败 [$operation]: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 打印完整的导航状态信息（包括FragmentManager和NavController）
     */
    fun printFullNavigationState(
        fragmentManager: FragmentManager,
        context: Context,
        operation: String,
        additionalInfo: String = ""
    ) {
        val fragmentManagerStackInfo = fragmentManager.fragments.joinToString(", ") { fragment ->
            val fragmentName = fragment.javaClass.simpleName
            val tag = fragment.tag ?: "null"
            val simplifiedTag = if (tag.length > 8) tag.substring(0, 8) else tag
            val lifecycleState = fragment.lifecycle.currentState.name
            "$fragmentName($simplifiedTag)[$lifecycleState]"
        }

        val fragmentManagerBackStackInfo = mutableListOf<String>()
        for (i in 0 until fragmentManager.backStackEntryCount) {
            try {
                val entry = fragmentManager.getBackStackEntryAt(i)
                val name = entry.name ?: "null"
                val simplifiedName = if (name.length > 8) name.substring(0, 8) else name
                fragmentManagerBackStackInfo.add(simplifiedName)
            } catch (e: Exception) {
                fragmentManagerBackStackInfo.add("error")
            }
        }

        Timber.tag(TAG).d("完整导航状态 [$operation]: $additionalInfo")
        Timber.tag(TAG).d("  FragmentManager活跃栈: [$fragmentManagerStackInfo]")
        Timber.tag(TAG).d("  FragmentManager回退栈: [${fragmentManagerBackStackInfo.joinToString(", ")}]")

        // 打印NavController的backQueue信息
        printNavControllerStateFromFragmentManager(fragmentManager, context, operation)
    }

    /**
     * 快速测试方法 - 在Fragment中调用此方法来打印当前状态
     */
    fun debugCurrentState(fragment: Fragment, operation: String = "debug") {
        try {
            Timber.tag(TAG).d("=== 开始调试NavController状态 [$operation] ===")

            // 检查Fragment状态
            Timber.tag(TAG).d("Fragment信息: ${fragment.javaClass.simpleName}, isAdded=${fragment.isAdded}, isDetached=${fragment.isDetached}")

            // 尝试多种方式获取NavController
            var navController: NavController? = null
            var navControllerSource = "unknown"

            // 方式1: 直接从Fragment获取
            try {
                navController = fragment.findNavController()
                navControllerSource = "fragment.findNavController()"
                Timber.tag(TAG).d("成功通过fragment.findNavController()获取NavController")
            } catch (e: Exception) {
                Timber.tag(TAG).w("通过fragment.findNavController()获取NavController失败: ${e.message}")
            }

            // 方式2: 从parentFragment获取
            if (navController == null) {
                try {
                    val parentFragment = fragment.parentFragment
                    Timber.tag(TAG).d("parentFragment: ${parentFragment?.javaClass?.simpleName}")

                    if (parentFragment is NavHostFragment) {
                        navController = parentFragment.navController
                        navControllerSource = "parentFragment.navController"
                        Timber.tag(TAG).d("成功通过parentFragment.navController获取NavController")
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).w("通过parentFragment获取NavController失败: ${e.message}")
                }
            }

            // 方式3: 从Activity的FragmentManager获取
            if (navController == null) {
                try {
                    val activity = fragment.activity
                    Timber.tag(TAG).d("activity: ${activity?.javaClass?.simpleName}")

                    val navHostFragment = activity?.supportFragmentManager?.fragments?.findTyped<NavHostFragment>()
                    if (navHostFragment != null) {
                        navController = navHostFragment.navController
                        navControllerSource = "activity.supportFragmentManager"
                        Timber.tag(TAG).d("成功通过activity.supportFragmentManager获取NavController")
                    } else {
                        Timber.tag(TAG).w("在activity.supportFragmentManager中未找到NavHostFragment")
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).w("通过activity获取NavController失败: ${e.message}")
                }
            }

            // 打印NavController状态
            if (navController != null) {
                Timber.tag(TAG).d("NavController获取成功，来源: $navControllerSource")
                val context = fragment.requireContext()
                printNavControllerState(navController, context, operation)

//                // 打印FragmentManager信息
//                try {
//                    val navHostFragment = navController.navHostFragment
//                    if (navHostFragment != null) {
//                        printFullNavigationState(navHostFragment.childFragmentManager, context, operation, "从NavController获取")
//                    }
//                } catch (e: Exception) {
//                    Timber.tag(TAG).w("获取NavHostFragment失败: ${e.message}")
//                }
            } else {
                Timber.tag(TAG).e("所有方式都无法获取NavController")

                // 打印Fragment层级信息
                Timber.tag(TAG).d("Fragment层级信息:")
                var currentFragment: Fragment? = fragment
                var level = 0
                while (currentFragment != null && level < 5) {
                    Timber.tag(TAG).d("  层级$level: ${currentFragment.javaClass.simpleName}, tag=${currentFragment.tag}, isAdded=${currentFragment.isAdded}")
                    currentFragment = currentFragment.parentFragment
                    level++
                }
            }

            Timber.tag(TAG).d("=== 调试信息结束 [$operation] ===")
        } catch (e: Exception) {
            Timber.tag(TAG).e("调试NavController状态失败 [$operation]: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 简单的NavController测试方法
     */
    fun testNavControllerAccess(fragment: Fragment, operation: String = "test") {
        Timber.tag(TAG).d("=== 测试NavController访问 [$operation] ===")

        // 方法1: fragment.findNavController()
        try {
            val navController1 = fragment.findNavController()
            Timber.tag(TAG).d("方法1成功: fragment.findNavController() -> ${navController1.javaClass.simpleName}")
        } catch (e: Exception) {
            Timber.tag(TAG).e("方法1失败: fragment.findNavController() -> ${e.message}")
        }

        // 方法2: 从parentFragment获取
        try {
            val parentFragment = fragment.parentFragment
            if (parentFragment is NavHostFragment) {
                val navController2 = parentFragment.navController
                Timber.tag(TAG).d("方法2成功: parentFragment.navController -> ${navController2.javaClass.simpleName}")
            } else {
                Timber.tag(TAG).w("方法2跳过: parentFragment不是NavHostFragment，而是${parentFragment?.javaClass?.simpleName}")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("方法2失败: parentFragment.navController -> ${e.message}")
        }

        // 方法3: 从Activity获取
        try {
            val activity = fragment.activity
            val navHostFragment = activity?.supportFragmentManager?.fragments?.findTyped<NavHostFragment>()
            if (navHostFragment != null) {
                val navController3 = navHostFragment.navController
                Timber.tag(TAG).d("方法3成功: activity.supportFragmentManager -> ${navController3.javaClass.simpleName}")
            } else {
                Timber.tag(TAG).w("方法3跳过: 在Activity中未找到NavHostFragment")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("方法3失败: activity.supportFragmentManager -> ${e.message}")
        }

        Timber.tag(TAG).d("=== 测试NavController访问结束 [$operation] ===")
    }

    /**
     * 打印Fragment层级结构
     */
    fun printFragmentHierarchy(fragment: Fragment, operation: String = "hierarchy") {
        Timber.tag(TAG).d("=== Fragment层级结构 [$operation] ===")

        var currentFragment: Fragment? = fragment
        var level = 0
        val maxLevel = 10

        while (currentFragment != null && level < maxLevel) {
            val indent = "  ".repeat(level)
            Timber.tag(TAG).d("${indent}层级$level: ${currentFragment.javaClass.simpleName}")
            Timber.tag(TAG).d("${indent}  tag: ${currentFragment.tag}")
            Timber.tag(TAG).d("${indent}  isAdded: ${currentFragment.isAdded}")
            Timber.tag(TAG).d("${indent}  isDetached: ${currentFragment.isDetached}")
            Timber.tag(TAG).d("${indent}  isVisible: ${currentFragment.isVisible}")

            if (currentFragment is NavHostFragment) {
                Timber.tag(TAG).d("${indent}  *** 这是NavHostFragment ***")
            }

            currentFragment = currentFragment.parentFragment
            level++
        }

        if (level >= maxLevel) {
            Timber.tag(TAG).w("Fragment层级过深，可能存在问题")
        }

        Timber.tag(TAG).d("=== Fragment层级结构结束 [$operation] ===")
    }
} 