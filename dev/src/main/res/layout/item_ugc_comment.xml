<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/vInvokeArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_3"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/ivMoreBtn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spaceHead" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideRight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="@dimen/dp_16" />

    <View
        android:id="@+id/vBgHighlight"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/color_f5f5f5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceHead"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_41"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivAvatar"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/spaceHead"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/spaceHead"
        tools:src="@drawable/icon_default_avatar" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvNickname"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_21"
        android:layout_marginStart="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvTime"
        app:layout_constraintEnd_toStartOf="@id/labelGroup"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/spaceHead"
        app:layout_constraintVertical_chainStyle="spread_inside"
        tools:text="SomebodySomebodySomebodySomebodySomebodySomebodySomebody" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTime"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_999999"
        app:layout_constraintBottom_toBottomOf="@id/spaceHead"
        app:layout_constraintStart_toStartOf="@id/tvNickname"
        app:layout_constraintTop_toBottomOf="@id/tvNickname"
        tools:text="Nov 12, 2022 14:12" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/labelGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="@id/tvNickname"
        app:layout_constraintEnd_toStartOf="@id/spaceMore"
        app:layout_constraintStart_toEndOf="@id/tvNickname"
        app:layout_constraintTop_toTopOf="@id/tvNickname" />

    <Space
        android:id="@+id/spaceMore"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_8"
        app:layout_constraintEnd_toStartOf="@id/ivMoreBtn"
        app:layout_constraintTop_toTopOf="@id/ivMoreBtn" />

    <ImageView
        android:id="@+id/ivMoreBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_6"
        android:src="@drawable/icon_more_vertical_3_points_1a1a1a_size23"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/spaceHead" />

    <View
        android:id="@+id/vRedDotMoreBtn"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_6"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivMoreBtn"
        app:layout_constraintTop_toTopOf="@id/ivMoreBtn"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tvContent"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:lineSpacingMultiplier="1.2"
        android:textColor="@color/color_1A1A1A"
        android:layout_marginEnd="@dimen/dp_28"
        android:textColorHighlight="@color/transparent"
        app:etv_EnableToggleClick="true"
        app:etv_ExtendClickScope="true"
        app:etv_MaxLinesOnShrink="5"
        app:etv_ToExpandHint="@string/more_cap"
        app:etv_ToExpandHintColor="@color/color_4AB4FF"
        app:etv_ToShrinkHint="@string/collapse_cap"
        app:etv_ToShrinkHintColor="@color/color_4AB4FF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/tvNickname"
        app:layout_constraintTop_toBottomOf="@id/spaceHead"
        tools:text="Comment Content" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cvImageContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:visibility="gone"
        app:cardBackgroundColor="@color/transparent"
        app:cardCornerRadius="@dimen/dp_8"
        app:cardElevation="0dp"
        app:layout_constraintStart_toStartOf="@id/tvNickname"
        app:layout_constraintTop_toBottomOf="@id/tvContent"
        app:layout_goneMarginTop="@dimen/dp_4"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/ivImage1"
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_86"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/ivImage2"
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_86"
            android:layout_marginStart="@dimen/dp_88"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/ivImage3"
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_86"
            android:layout_marginStart="@dimen/dp_176"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvPinLabel"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_8"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:text="@string/pinned_comment"
        android:textColor="@color/color_666666"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/tvAuthorLikeLabel"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/tvNickname"
        app:layout_constraintTop_toBottomOf="@id/cvImageContainer"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvAuthorLikeLabel"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_8"
        android:background="@drawable/shape_fff0ee_corner_19"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:text="@string/comment_label_author_liked"
        android:textColor="@color/color_FF5F42"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/tvAuthorRepliedLabel"
        app:layout_constraintStart_toEndOf="@id/tvPinLabel"
        app:layout_constraintTop_toBottomOf="@id/cvImageContainer"
        app:layout_goneMarginStart="@dimen/dp_0"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvAuthorRepliedLabel"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:text="@string/comment_label_author_replied"
        android:textColor="@color/color_666666"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvAuthorLikeLabel"
        app:layout_constraintTop_toBottomOf="@id/cvImageContainer"
        app:layout_goneMarginStart="@dimen/dp_0"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tvPinLabel,tvAuthorLikeLabel,tvAuthorRepliedLabel" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLikeCount"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:drawableStart="@drawable/selector_comment_like"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:minWidth="@dimen/dp_51"
        android:textColor="@color/color_666666"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier"
        tools:text="26" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavLikeAnim"
        android:layout_width="@dimen/dp_33"
        android:layout_height="@dimen/dp_33"
        android:layout_marginStart="-7dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvLikeCount"
        app:layout_constraintStart_toStartOf="@id/tvLikeCount"
        app:layout_constraintTop_toTopOf="@id/tvLikeCount"
        app:lottie_autoPlay="false"
        app:lottie_fileName="community_like.zip"
        app:lottie_progress="1"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCommentCount"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_26"
        android:drawableStart="@drawable/ic_comment_666666_size_20"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:textColor="@color/color_666666"
        app:layout_constraintEnd_toStartOf="@id/tvLikeCount"
        app:layout_constraintTop_toBottomOf="@id/barrier"
        tools:text="26" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_12"
        app:layout_constraintTop_toBottomOf="@id/tvLikeCount" />

    <View
        android:id="@+id/startLine"
        android:layout_width="@dimen/dp_05"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_34"
        android:layout_marginTop="@dimen/dp_6"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spaceHead" />

</androidx.constraintlayout.widget.ConstraintLayout>