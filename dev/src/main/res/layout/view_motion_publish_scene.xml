<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/motion_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layoutDescription="@xml/motion_publish_scene">

    <View
        android:id="@+id/vBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTemplate"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_248"
        android:layout_height="@dimen/dp_60"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_12"
        android:background="@drawable/bg_choose_publish_item_gradient"
        android:drawableStart="@drawable/ic_publish_scene_moments"
        android:drawablePadding="@dimen/dp_14"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_6"
        android:text="@string/template_all_cap"
        app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvPost"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_248"
        android:layout_height="@dimen/dp_60"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_choose_publish_item_gradient"
        android:drawableStart="@drawable/ic_publish_scene_post"
        android:drawablePadding="@dimen/dp_14"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_6"
        android:text="@string/post_all_cap"
        app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene" />

    <com.lihang.ShadowLayout
        android:id="@+id/slChoosePublishScene"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/tab_layout_height"
        android:background="@color/white"
        app:hl_cornerRadius="@dimen/dp_100"
        app:hl_shadowColor="@color/black"
        app:hl_shadowLimit="@dimen/dp_16"
        app:hl_shadowOffsetY="@dimen/dp_6"
        app:layout_constraintBottom_toTopOf="@id/space"
        app:layout_constraintEnd_toEndOf="parent">

        <View
            android:id="@+id/vClick"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_gravity="center"
            android:background="@color/color_FFDC1C"
            android:foreground="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            />

    </com.lihang.ShadowLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPublishPlaceHolder"
        android:layout_height="@dimen/dp_64"
        android:layout_width="@dimen/dp_64"
        android:clickable="false"
        android:background="@null"
        app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
        app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
        app:layout_constraintStart_toStartOf="@id/slChoosePublishScene"
        app:layout_constraintTop_toTopOf="@id/slChoosePublishScene"
        >
        <ImageView
            android:id="@+id/ivPostPlaceHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_community_post"
            app:layout_constraintBottom_toTopOf="@id/tvPostPlaceHolder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvPostPlaceHolder"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:text="@string/post"
            app:layout_constraintTop_toBottomOf="@id/ivPostPlaceHolder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivPublish"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:src="@drawable/ic_publish_post_add"
        app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
        app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
        app:layout_constraintStart_toStartOf="@id/slChoosePublishScene"
        app:layout_constraintTop_toTopOf="@id/slChoosePublishScene"
        app:tint="@color/textColorPrimary" />

    <Space
        android:id="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.motion.widget.MotionLayout>