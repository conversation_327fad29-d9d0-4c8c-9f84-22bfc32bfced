<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.refresh.SpeedWrapNestedScrollableMetaRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/refresh"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <!-- 用于位移loadingView -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 个人主页整体也能上下滑动, 包一层 NestedScrollView 解决滑动冲突 -->
        <androidx.core.widget.NestedScrollView
            android:id="@+id/tabScrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <com.socialplay.gpark.ui.view.WrapEpoxyRecyclerView
                android:id="@+id/rvTab"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_24"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_12"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
        </androidx.core.widget.NestedScrollView>

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tabScrollView" />

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/loadingUgc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.socialplay.gpark.ui.view.refresh.SpeedWrapNestedScrollableMetaRefreshLayout>