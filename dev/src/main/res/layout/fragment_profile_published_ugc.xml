<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.WrapNestedScrollableHost xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:speed_monitor="true">

    <!-- 个人主页整体也能上下滑动, 包一层 NestedScrollView 解决滑动冲突 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollViewWaitingReleased"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/layoutWaitingReleased"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16"
            android:background="@drawable/bg_ffdc1c_round_12_stoke"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|bottom"
                android:layout_marginEnd="@dimen/dp_20"
                android:src="@drawable/unpublish_project_info_icon" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:layout_marginEnd="@dimen/dp_12"
                android:src="@drawable/icon_arrow_right_16_black" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvWaitingReleased"
                style="@style/MetaTextView.S12.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_80"
                android:text="@string/map_un_published_tips_text"
                android:textColor="@color/color_451A03" />
        </FrameLayout>
    </androidx.core.widget.NestedScrollView>

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scrollViewWaitingReleased">
        <!-- 用于位移loadingView -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvUgc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/dp_8"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"
                tools:listitem="@layout/item_profile_maps" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/loadingUgc"
                android:layout_gravity="center_horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone" />

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

</com.socialplay.gpark.ui.view.WrapNestedScrollableHost>