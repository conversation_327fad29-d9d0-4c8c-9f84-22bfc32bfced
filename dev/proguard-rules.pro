# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.kts.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile


#-keepattributes SourceFile,LineNumberTable        # Keep file names and line numbers.

#GSON 需要泛型签名
-keepattributes Signature
-keep class com.crashlytics.** { *; }
-dontoptimize

# field and method names
-obfuscationdictionary proguard-obfuscation-dictionary.txt
#class names
-classobfuscationdictionary proguard-obfuscation-dictionary.txt
#package names.
-packageobfuscationdictionary proguard-obfuscation-dictionary.txt

# 删除Kotlin编译时可能生成显示变量的方法
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    public static void checkExpressionValueIsNotNull(java.lang.Object, java.lang.String);
    public static void checkFieldIsNotNull(java.lang.Object, java.lang.String);
    public static void checkFieldIsNotNull(java.lang.Object, java.lang.String, java.lang.String);
    public static void checkNotNull(java.lang.Object);
    public static void checkNotNull(java.lang.Object, java.lang.String);
    public static void checkNotNullExpressionValue(java.lang.Object, java.lang.String);
    public static void checkNotNullParameter(java.lang.Object, java.lang.String);
    public static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
    public static void checkReturnedValueIsNotNull(java.lang.Object, java.lang.String);
    public static void throwUninitializedPropertyAccessException(java.lang.String);
}

# 防止实体类成员混淆
-keep class com.socialplay.gpark.data.model.** { *; }

# End   各部门反射安卓的API列表
-keep class okhttp3.** { *; }

-keep class com.meta.sdk.open.** {*;}
-keep class com.meta.sdk.open.mix.** {*;}

-keepclassmembernames class com.socialplay.gpark.data.base.DataResult { *; }
-keepclassmembernames class com.socialplay.gpark.data.base.ApiResult { *; }
-keepclassmembernames class com.socialplay.gpark.data.base.PagingApiResult { *; }
-keep @com.socialplay.gpark.util.KeepClass class *

# 开发者页面字段Keep
-keep class com.socialplay.gpark.BuildConfig { public <fields>; }

-dontwarn com.android.installreferrer
-keep class com.appsflyer.** { *; }

# START EventBus混淆配置
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# And if you use AsyncExecutor:
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}
# END EventBus混淆配置

# START tencent 腾讯IM混淆配置
-keep class com.tencent.imsdk.** { *; }
# END tencent 腾讯IM混淆配置

#keep gosn 防止UE内核调用失败
-keep class com.google.gson.Gson {*;}
-keep class androidx.core.app.ActivityCompat {*;}
-keep class androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback {*;}
-keep class androidx.core.app.NotificationManagerCompat {*;}
-keep class androidx.core.app.NotificationCompat {*;}
-keep class androidx.core.content.FileProvider {*;}
-keep class androidx.core.content.ContextCompat {*;}

# MW内核使用Androidx FragmentActivity
#-keep class androidx.** { *; }
-keep class com.google.gson.reflect.** {*;}

#AppLovinSdk keep gms
-keep public class com.google.android.gms.** { public protected *; }

-keep class androidx.recyclerview.widget.**{*;}
-keep class androidx.viewpager2.widget.**{*;}

-keep class org.apache.tools.zip.** { *; }

# GlideWebpDecoder
-keep public class com.bumptech.glide.integration.webp.WebpImage { *; }
-keep public class com.bumptech.glide.integration.webp.WebpFrame { *; }
-keep public class com.bumptech.glide.integration.webp.WebpBitmapFactory { *; }
# ----------------声网-----------------
-keep class io.agora.**{*;}


-keep class com.socialplay.gpark.overseabridge.OverseaBridgeProviderImpl

-keep class com.google.gson.** {*;}
#-keep class * extends com.google.gson.reflect.TypeToken{*;}
-keepclassmembernames class * extends com.google.gson.reflect.TypeToken{*;}

# Ensure the DebugMetadata annotation is not included in the APK.
#-checkdiscard @interface kotlin.coroutines.jvm.internal.DebugMetadata

-assumenosideeffects public class kotlin.coroutines.jvm.internal.BaseContinuationImpl {
  private kotlin.coroutines.jvm.internal.DebugMetadata getDebugMetadataAnnotation() return null;
  public java.lang.StackTraceElement getStackTraceElement() return null;
  public java.lang.String[] getSpilledVariableFieldMapping() return null;
}

 # Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
     boolean getASSERTIONS_ENABLED() return false;
     boolean getDEBUG() return false;
     boolean getRECOVER_STACK_TRACES() return false;
 }

# 这个Callback只有一个方法且方法没参数，可能会被聚合，聚合后会存在问题，所以需要Keep
-keep class com.meta.verse.bridge.isolate.server.IUEService$IEngineReadyCallback{
    *;
}

-keep class * extends com.meta.verse.bridge.isolate.server.IUEService$IEngineReadyCallback {
    *;
}

-keep class com.google.android.material.appbar.AppBarLayout$BaseBehavior {
    <fields>;
    boolean isOffsetAnimatorRunning();
 }

# viewpager2 allow loss state
-keep class androidx.viewpager2.adapter.FragmentStateAdapter {
   final androidx.fragment.app.FragmentManager mFragmentManager;
}
-keep class androidx.viewpager2.widget.ViewPager2 {
    private int mPendingCurrentItem;
    private android.os.Parcelable mPendingAdapterState;
}
-keep class androidx.viewpager2.widget.ViewPager2$SavedState {
    int mCurrentItem;
    android.os.Parcelable mAdapterState;
}


-keepclassmembers class * extends androidx.viewbinding.ViewBinding {
    public static *** bind(android.view.View);
}

-keep,allowoptimization class * implements androidx.viewbinding.ViewBinding {
    public static *** bind(android.view.View);
    public static *** inflate(...);
}

-keepclassmembers class ** extends com.airbnb.mvrx.MavericksViewModel {
    ** Companion;
}
-keepclassmembers,includedescriptorclasses,allowobfuscation class ** implements com.airbnb.mvrx.MavericksState {
   *;
}
-keepnames class com.airbnb.mvrx.MavericksState
-keepnames class * implements com.airbnb.mvrx.MavericksState
-keepnames class * implements com.airbnb.mvrx.MavericksViewModelFactory

-keep class com.socialplay.gpark.function.mw.launch.exception.BaseTSLaunchException{*;}
-keep class  * extends com.socialplay.gpark.function.mw.launch.exception.BaseTSLaunchException{*;}

-keep class com.socialplay.gpark.di.ApiInterceptorIOException{*;}


# 图片选择器
-keep class com.luck.picture.lib.** { *; }
-keep class com.meta.lib.api.resolve.data.model.** { *; }
-keepclassmembers class com.meta.ai.model.** { *; }
-keep class com.meta.biz.mgs.data.** { *; }
-keep class com.meta.biz.mgs.im.** { *; }
-keep class com.ly123.metacloud.data.** { *; }
-keep class com.ly123.tes.mgs.metacloud.origin.** { *; }

-keep class com.meta.box.biz.friend.model.LabelInfo {*;}
# Keep the Navigation classes
-keep class androidx.navigation.** { *; }
# Keep all Fragment classes that are used in the Navigation graph
-keep public class * extends androidx.fragment.app.Fragment
# Keep all classes that are used as arguments in the Navigation graph
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
-keep class com.meta.web.model.** { *; }
