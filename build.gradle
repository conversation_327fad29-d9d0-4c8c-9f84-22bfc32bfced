import java.security.spec.EncodedKeySpec

// Top-level build file where you can add configuration options common to all sub-projects/modules.


buildscript {
//    apply from: "mw.engine.params.gradle"
    apply from: "mw.engine.params.gradle.kts"
    apply from: "multilingual.fetch.params.gradle.kts"
    apply from: 'depVersionConstraints/plugin.version.gradle'
    repositories {
        mavenLocal()
        maven {
            setAllowInsecureProtocol(true)
            setUrl("http://maven.metaapp.cn/repository/maven-releases/")
        }

        // 使用阿里云maven仓库 https://developer.aliyun.com/mvn/guide
        google() // https://maven.google.com/
        maven { setUrl("https://maven.aliyun.com/repository/google") }

        mavenCentral() //  jcenter()
        maven { setUrl("https://maven.aliyun.com/repository/public") }

        // https://repo1.maven.org/maven2/
        maven { setUrl("https://maven.aliyun.com/repository/central") }

        // gradle-plugin
        maven { setUrl("https://maven.aliyun.com/repository/gradle-plugin") }

        maven { setUrl("https://jitpack.io") }

        maven { url = uri("https://artifacts.applovin.com/android") }
        maven { url = uri("https://artifact.bytedance.com/repository/releases") }
        maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
        maven { url = uri("https://developer.huawei.com/repo/") }
        maven { url = uri("https://developer.hihonor.com/repo/") }
    }


    dependencies {
        classpath(gradlePlugin.androidBuildGradle)
        classpath(gradlePlugin.kotlinGradlePlugin)
        classpath(gradlePlugin.ksp)
        classpath(gradlePlugin.navigationSafeArgsGradlePlugin)
        classpath(gradlePlugin.aabResGuardPlugin)
        classpath(gradlePlugin.mwPlugin)
        classpath(gradlePlugin.metaPlugin)
        classpath(gradlePlugin.metaCompatPlugin)
        classpath(gradlePlugin.multilingual)
        classpath(gradlePlugin.fontscale)
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

plugins {
    id "dep-version-constraints" apply false
}

subprojects {
    apply plugin: "dep-version-constraints"
    EnvConfigs.INSTANCE.check(it)

//    GParkEnvConfig.INSTANCE.check(it)
//    PartyEnvConfig.INSTANCE.check(it)

    configurations.maybeCreate("lintChecks")
    dependencies.add("lintChecks",Libs.META_LINTX)
}

rootProject.apply plugin: "mw.engine"

allprojects {

    repositories {
        mavenLocal()
        google() // https://maven.google.com/
        maven { setUrl("https://maven.aliyun.com/repository/google") }
        maven {
            setAllowInsecureProtocol(true)
            setUrl("http://maven.metaapp.cn/repository/maven-releases/")
        }
        mavenCentral() //  jcenter()
        maven { setUrl("https://maven.aliyun.com/repository/public") }

        // https://repo1.maven.org/maven2/
        maven { setUrl("https://maven.aliyun.com/repository/central") }

        maven { setUrl("https://maven.rongcloud.cn/repository/maven-releases/") }
        maven { setUrl("https://jitpack.io") }
        maven { setUrl("https://artifact.bytedance.com/repository/AwemeOpenSDK") }
        maven { url = uri("https://artifact.bytedance.com/repository/releases") }
        maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
        maven { url = uri("https://developer.huawei.com/repo/") }
        maven { url = uri("https://developer.hihonor.com/repo/") }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}